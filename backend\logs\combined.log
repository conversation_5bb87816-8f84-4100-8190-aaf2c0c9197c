{"level":"info","message":"::1 - - [05/Aug/2025:18:48:47 +0000] \"GET /health HTTP/1.1\" 200 237 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"fpt-unihub-backend","timestamp":"2025-08-05T18:48:47.205Z"}
{"duration":"751ms","ip":"::1","level":"info","message":"HTTP Request","method":"GET","service":"fpt-unihub-backend","status":200,"timestamp":"2025-08-05T18:48:47.467Z","url":"/health","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"level":"info","message":"::1 - - [05/Aug/2025:18:48:49 +0000] \"GET /favicon.ico HTTP/1.1\" 500 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"fpt-unihub-backend","timestamp":"2025-08-05T18:48:49.375Z"}
{"duration":"445ms","ip":"::1","level":"error","message":"HTTP Request Error","method":"GET","service":"fpt-unihub-backend","status":500,"timestamp":"2025-08-05T18:48:49.384Z","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"level":"info","message":"::1 - - [05/Aug/2025:21:51:14 +0000] \"GET /api/dashboard/public-stats HTTP/1.1\" 500 195 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"fpt-unihub-backend","timestamp":"2025-08-05T21:51:14.229Z"}
{"duration":"202ms","ip":"::1","level":"error","message":"HTTP Request Error","method":"GET","service":"fpt-unihub-backend","status":500,"timestamp":"2025-08-05T21:51:14.288Z","url":"/public-stats","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"level":"info","message":"::1 - - [05/Aug/2025:21:51:15 +0000] \"GET /favicon.ico HTTP/1.1\" 500 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"fpt-unihub-backend","timestamp":"2025-08-05T21:51:15.928Z"}
{"duration":"365ms","ip":"::1","level":"error","message":"HTTP Request Error","method":"GET","service":"fpt-unihub-backend","status":500,"timestamp":"2025-08-05T21:51:16.025Z","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"level":"info","message":"::1 - - [05/Aug/2025:22:06:30 +0000] \"GET /api/dashboard/public-stats HTTP/1.1\" 500 195 \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"fpt-unihub-backend","timestamp":"2025-08-05T22:06:30.057Z"}
{"duration":"59ms","ip":"::1","level":"error","message":"HTTP Request Error","method":"GET","service":"fpt-unihub-backend","status":500,"timestamp":"2025-08-05T22:06:30.064Z","url":"/public-stats","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"level":"info","message":"::1 - - [05/Aug/2025:22:06:31 +0000] \"GET /favicon.ico HTTP/1.1\" 500 - \"-\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","service":"fpt-unihub-backend","timestamp":"2025-08-05T22:06:31.434Z"}
{"duration":"111ms","ip":"::1","level":"error","message":"HTTP Request Error","method":"GET","service":"fpt-unihub-backend","status":500,"timestamp":"2025-08-05T22:06:31.493Z","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
