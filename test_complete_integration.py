#!/usr/bin/env python3
"""
Complete integration test for the chatbot system
Tests auto-startup with backend and floating button functionality
"""
import subprocess
import time
import requests
import sys
import os
from pathlib import Path

def test_backend_auto_startup():
    """Test if backend auto-starts chatbot"""
    print("🔍 Testing backend auto-startup of chatbot...")
    
    backend_dir = Path(__file__).parent / "backend"
    
    # Start backend
    print("🚀 Starting backend...")
    backend_process = subprocess.Popen(
        ["npm", "start"],
        cwd=backend_dir,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if os.name == 'nt' else 0
    )
    
    try:
        # Wait for backend to start
        print("⏳ Waiting for backend to start...")
        backend_started = False
        for i in range(30):
            try:
                response = requests.get("http://localhost:5000/api/health", timeout=2)
                if response.status_code == 200:
                    print("✅ Backend started successfully")
                    backend_started = True
                    break
            except:
                pass
            time.sleep(1)
        
        if not backend_started:
            print("❌ Backend failed to start")
            return False
        
        # Wait for chatbot to auto-start
        print("⏳ Waiting for chatbot auto-startup...")
        chatbot_started = False
        for i in range(60):  # Wait up to 60 seconds for chatbot
            try:
                response = requests.get("http://localhost:8001/health", timeout=2)
                if response.status_code == 200:
                    print("✅ Chatbot auto-started successfully")
                    chatbot_started = True
                    break
            except:
                pass
            time.sleep(1)
        
        if not chatbot_started:
            print("❌ Chatbot failed to auto-start")
            return False
        
        # Test chatbot functionality
        print("🧪 Testing chatbot functionality...")
        try:
            response = requests.post(
                "http://localhost:8001/chat",
                json={"message": "Hello, test message"},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Chatbot response: {data.get('response', '')[:100]}...")
                return True
            else:
                print(f"❌ Chatbot test failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Chatbot test failed: {e}")
            return False
            
    finally:
        # Cleanup
        print("🧹 Cleaning up...")
        try:
            if os.name == 'nt':
                subprocess.run(["taskkill", "/F", "/T", "/PID", str(backend_process.pid)], 
                             capture_output=True)
            else:
                backend_process.terminate()
                backend_process.wait(timeout=5)
        except:
            pass

def test_frontend_floating_button():
    """Test if frontend has floating button"""
    print("🔍 Testing frontend floating button...")
    
    frontend_dir = Path(__file__).parent / "frontend"
    
    # Check if ChatbotFloatingButton component exists
    floating_button_file = frontend_dir / "src" / "components" / "ui" / "chatbot-floating-button.tsx"
    if not floating_button_file.exists():
        print("❌ ChatbotFloatingButton component not found")
        return False
    
    print("✅ ChatbotFloatingButton component exists")
    
    # Check if it's imported in layout
    layout_file = frontend_dir / "src" / "app" / "layout.tsx"
    if layout_file.exists():
        with open(layout_file, 'r', encoding='utf-8') as f:
            layout_content = f.read()
            if 'ChatbotFloatingButton' in layout_content:
                print("✅ ChatbotFloatingButton imported in layout")
                return True
            else:
                print("❌ ChatbotFloatingButton not imported in layout")
                return False
    else:
        print("❌ Layout file not found")
        return False

def test_database_integration():
    """Test database integration"""
    print("🔍 Testing database integration...")
    
    try:
        import psycopg2
        conn = psycopg2.connect(
            host="localhost",
            port="5432",
            database="Fbot_Management",
            user="postgres",
            password="chiendz098"
        )
        
        cursor = conn.cursor()
        
        # Test if required tables exist
        tables_to_check = ['Todos', 'classes', 'users', 'ChatHistory']
        for table in tables_to_check:
            cursor.execute(f"SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = '{table}');")
            exists = cursor.fetchone()[0]
            if exists:
                print(f"✅ Table '{table}' exists")
            else:
                print(f"❌ Table '{table}' missing")
                return False
        
        conn.close()
        print("✅ Database integration test passed")
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False

def main():
    """Run all integration tests"""
    print("🚀 Starting Complete Integration Tests")
    print("=" * 50)
    
    tests = [
        ("Database Integration", test_database_integration),
        ("Frontend Floating Button", test_frontend_floating_button),
        ("Backend Auto-Startup", test_backend_auto_startup),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
        
        time.sleep(2)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 INTEGRATION TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All integration tests passed!")
        print("\n📝 USAGE INSTRUCTIONS:")
        print("1. Run 'cd backend && npm start' to start the system")
        print("2. The chatbot will auto-start with the backend")
        print("3. Open http://localhost:3000 for the frontend")
        print("4. Look for the floating chat button in bottom-right corner")
        print("5. Enjoy your integrated chatbot system! 🤖✨")
        return 0
    else:
        print("⚠️ Some integration tests failed. Please check the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
