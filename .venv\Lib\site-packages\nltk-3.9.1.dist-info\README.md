# Natural Language Toolkit (NLTK)
[![PyPI](https://img.shields.io/pypi/v/nltk.svg)](https://pypi.python.org/pypi/nltk)
![CI](https://github.com/nltk/nltk/actions/workflows/ci.yaml/badge.svg?branch=develop)

NLTK -- the Natural Language Toolkit -- is a suite of open source Python
modules, data sets, and tutorials supporting research and development in Natural
Language Processing. NLTK requires Python version 3.8, 3.9, 3.10, 3.11 or 3.12.

For documentation, please visit [nltk.org](https://www.nltk.org/).


## Contributing

Do you want to contribute to NLTK development? Great!
Please read [CONTRIBUTING.md](CONTRIBUTING.md) for more details.

See also [how to contribute to NLTK](https://www.nltk.org/contribute.html).


## Donate

Have you found the toolkit helpful?  Please support NLTK development by donating
to the project via PayPal, using the link on the NLTK homepage.


## Citing

If you publish work that uses NLTK, please cite the NLTK book, as follows:

    <PERSON>, <PERSON>, <PERSON> and <PERSON><PERSON> (2009).
    Natural Language Processing with Python.  O'Reilly Media Inc.


## Copyright

Copyright (C) 2001-2024 NLTK Project

For license information, see [LICENSE.txt](LICENSE.txt).

[AUTHORS.md](AUTHORS.md) contains a list of everyone who has contributed to NLTK.


### Redistributing

- NLTK source code is distributed under the Apache 2.0 License.
- NLTK documentation is distributed under the Creative Commons
  Attribution-Noncommercial-No Derivative Works 3.0 United States license.
- NLTK corpora are provided under the terms given in the README file for each
  corpus; all are redistributable and available for non-commercial use.
- NLTK may be freely redistributed, subject to the provisions of these licenses.
