/**
 * Real-time Collaboration Components
 * Provides live collaboration features for study groups and assignments
 */

'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useWebSocket } from '@/lib/websocket-service';
import { useAuth } from '@/lib/auth-context';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { 
  Users, 
  MessageSquare, 
  Eye, 
  Edit3, 
  Send, 
  UserPlus,
  Circle,
  Wifi,
  WifiOff
} from 'lucide-react';
import { toast } from 'react-hot-toast';

interface CollaborationUser {
  id: string;
  name: string;
  avatar?: string;
  status: 'online' | 'away' | 'offline';
  cursor?: { x: number; y: number };
  lastSeen: Date;
}

interface CollaborationMessage {
  id: string;
  userId: string;
  userName: string;
  content: string;
  timestamp: Date;
  type: 'message' | 'system' | 'activity';
}

interface CollaborationProps {
  roomId: string;
  roomType: 'study_group' | 'assignment' | 'class';
  title: string;
}

export function RealTimeCollaboration({ roomId, roomType, title }: CollaborationProps) {
  const { user } = useAuth();
  const { isConnected, send, subscribe } = useWebSocket();
  
  const [activeUsers, setActiveUsers] = useState<CollaborationUser[]>([]);
  const [messages, setMessages] = useState<CollaborationMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState<string[]>([]);
  const [showChat, setShowChat] = useState(false);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (!isConnected || !user) return;

    // Join collaboration room
    send('join_room', {
      roomId,
      roomType,
      user: {
        id: user.id,
        name: user.name,
        avatar: user.image
      }
    });

    // Subscribe to collaboration events
    const unsubscribeUsers = subscribe('room_users', (data) => {
      setActiveUsers(data.users);
    });

    const unsubscribeMessages = subscribe('room_message', (data) => {
      setMessages(prev => [...prev, data.message]);
      scrollToBottom();
    });

    const unsubscribeTyping = subscribe('user_typing', (data) => {
      if (data.userId !== user.id) {
        setIsTyping(prev => {
          if (data.isTyping) {
            return [...prev.filter(id => id !== data.userId), data.userId];
          } else {
            return prev.filter(id => id !== data.userId);
          }
        });
      }
    });

    const unsubscribeActivity = subscribe('user_activity', (data) => {
      if (data.roomId === roomId) {
        const activityMessage: CollaborationMessage = {
          id: `activity_${Date.now()}`,
          userId: 'system',
          userName: 'System',
          content: data.message,
          timestamp: new Date(),
          type: 'activity'
        };
        setMessages(prev => [...prev, activityMessage]);
      }
    });

    return () => {
      // Leave room on cleanup
      send('leave_room', { roomId });
      unsubscribeUsers();
      unsubscribeMessages();
      unsubscribeTyping();
      unsubscribeActivity();
    };
  }, [isConnected, user, roomId, roomType, send, subscribe]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = () => {
    if (!newMessage.trim() || !user) return;

    const message: CollaborationMessage = {
      id: `msg_${Date.now()}`,
      userId: user.id,
      userName: user.name || 'Anonymous',
      content: newMessage.trim(),
      timestamp: new Date(),
      type: 'message'
    };

    send('room_message', {
      roomId,
      message
    });

    setNewMessage('');
    handleStopTyping();
  };

  const handleTyping = (value: string) => {
    setNewMessage(value);
    
    if (!user) return;

    // Send typing indicator
    send('user_typing', {
      roomId,
      userId: user.id,
      isTyping: value.length > 0
    });

    // Clear previous timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set timeout to stop typing indicator
    if (value.length > 0) {
      typingTimeoutRef.current = setTimeout(() => {
        handleStopTyping();
      }, 2000);
    }
  };

  const handleStopTyping = () => {
    if (user) {
      send('user_typing', {
        roomId,
        userId: user.id,
        isTyping: false
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-500';
      case 'away': return 'bg-yellow-500';
      default: return 'bg-gray-400';
    }
  };

  return (
    <div className="space-y-4">
      {/* Connection Status */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {isConnected ? (
            <Wifi className="h-4 w-4 text-green-500" />
          ) : (
            <WifiOff className="h-4 w-4 text-red-500" />
          )}
          <span className="text-sm text-gray-600">
            {isConnected ? 'Connected' : 'Disconnected'}
          </span>
        </div>
        
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowChat(!showChat)}
          className="relative"
        >
          <MessageSquare className="h-4 w-4 mr-2" />
          Chat
          {messages.length > 0 && (
            <Badge className="ml-2 h-5 w-5 p-0 text-xs">
              {messages.length}
            </Badge>
          )}
        </Button>
      </div>

      {/* Active Users */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center">
            <Users className="h-5 w-5 mr-2" />
            Active Users ({activeUsers.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <AnimatePresence>
              {activeUsers.map((collaborator) => (
                <motion.div
                  key={collaborator.id}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  className="flex items-center space-x-2 bg-gray-50 rounded-full px-3 py-1"
                >
                  <div className="relative">
                    <Avatar className="h-6 w-6">
                      <AvatarImage src={collaborator.avatar} />
                      <AvatarFallback className="text-xs">
                        {collaborator.name.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                    <div className={`absolute -bottom-0.5 -right-0.5 h-3 w-3 rounded-full border-2 border-white ${getStatusColor(collaborator.status)}`} />
                  </div>
                  <span className="text-sm font-medium">{collaborator.name}</span>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </CardContent>
      </Card>

      {/* Chat Panel */}
      <AnimatePresence>
        {showChat && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">
                  Chat - {title}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Messages */}
                <div className="h-64 overflow-y-auto space-y-2 border rounded-lg p-3 bg-gray-50">
                  {messages.map((message) => (
                    <motion.div
                      key={message.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className={`flex items-start space-x-2 ${
                        message.type === 'system' || message.type === 'activity'
                          ? 'justify-center'
                          : message.userId === user?.id
                          ? 'flex-row-reverse space-x-reverse'
                          : ''
                      }`}
                    >
                      {message.type === 'message' && (
                        <Avatar className="h-6 w-6 mt-1">
                          <AvatarFallback className="text-xs">
                            {message.userName.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                      )}
                      <div className={`max-w-xs ${
                        message.type === 'system' || message.type === 'activity'
                          ? 'text-center text-gray-500 text-sm italic'
                          : message.userId === user?.id
                          ? 'bg-blue-500 text-white rounded-lg px-3 py-2'
                          : 'bg-white border rounded-lg px-3 py-2'
                      }`}>
                        {message.type === 'message' && message.userId !== user?.id && (
                          <div className="text-xs text-gray-500 mb-1">
                            {message.userName}
                          </div>
                        )}
                        <div className="text-sm">{message.content}</div>
                        <div className="text-xs opacity-70 mt-1">
                          {message.timestamp.toLocaleTimeString()}
                        </div>
                      </div>
                    </motion.div>
                  ))}
                  
                  {/* Typing Indicators */}
                  {isTyping.length > 0 && (
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="flex items-center space-x-2 text-gray-500 text-sm italic"
                    >
                      <div className="flex space-x-1">
                        {[0, 1, 2].map((i) => (
                          <motion.div
                            key={i}
                            className="w-1 h-1 bg-gray-400 rounded-full"
                            animate={{
                              scale: [1, 1.5, 1],
                              opacity: [0.5, 1, 0.5],
                            }}
                            transition={{
                              duration: 1,
                              repeat: Infinity,
                              delay: i * 0.2,
                            }}
                          />
                        ))}
                      </div>
                      <span>
                        {isTyping.length === 1 
                          ? `Someone is typing...`
                          : `${isTyping.length} people are typing...`
                        }
                      </span>
                    </motion.div>
                  )}
                  
                  <div ref={messagesEndRef} />
                </div>

                {/* Message Input */}
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={newMessage}
                    onChange={(e) => handleTyping(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                    placeholder="Type a message..."
                    className="flex-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    disabled={!isConnected}
                  />
                  <Button
                    onClick={handleSendMessage}
                    disabled={!newMessage.trim() || !isConnected}
                    size="sm"
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
