#!/usr/bin/env python3
"""
Improved chatbot startup script with better error handling and port management
"""
import uvicorn
import socket
import sys
import os
from pathlib import Path

def find_free_port(start_port=8001, max_attempts=10):
    """Find a free port starting from start_port"""
    for port in range(start_port, start_port + max_attempts):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    raise RuntimeError(f"Could not find a free port in range {start_port}-{start_port + max_attempts}")

def main():
    try:
        # Ensure we're in the right directory
        script_dir = Path(__file__).parent
        os.chdir(script_dir)
        
        # Find a free port
        port = find_free_port()
        print(f"Starting chatbot on port {port}")
        
        # Update environment variable for backend service
        os.environ['CHATBOT_API_URL'] = f'http://localhost:{port}'
        
        # Import the app after setting up the environment
        from main import app
        
        # Start the server
        uvicorn.run(
            app, 
            host="0.0.0.0", 
            port=port,
            log_level="info",
            access_log=True
        )
        
    except Exception as e:
        print(f"Failed to start chatbot: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
