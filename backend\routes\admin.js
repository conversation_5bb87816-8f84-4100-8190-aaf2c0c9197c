const express = require('express');
const router = express.Router();
const { auth, authorize } = require('../middleware/auth');
const { asyncHandler } = require('../middleware/errorHandler');
const cronService = require('../services/cronService');
const emailService = require('../services/emailService');
const Todo = require('../models/Todo');
const User = require('../models/User');
const { Op } = require('sequelize');

// Admin middleware - chỉ admin mới có thể truy cập
const adminAuth = [auth, authorize('admin')];

// Lấy trạng thái của tất cả cron jobs
router.get('/cron/status', adminAuth, asyncHandler(async (req, res) => {
  const status = cronService.getJobsStatus();
  res.json({
    success: true,
    data: status
  });
}));

// Ch<PERSON>y một cron job ngay lập tức (để test)
router.post('/cron/run/:jobName', adminAuth, asyncHandler(async (req, res) => {
  const { jobName } = req.params;
  
  const validJobs = ['taskReminder', 'overdueCheck', 'dailyDigest', 'cleanupNotifications', 'updateTaskStatus'];
  
  if (!validJobs.includes(jobName)) {
    return res.status(400).json({
      success: false,
      message: 'Invalid job name',
      validJobs
    });
  }

  try {
    await cronService.runJobNow(jobName);
    res.json({
      success: true,
      message: `Job '${jobName}' executed successfully`
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: `Failed to run job '${jobName}': ${error.message}`
    });
  }
}));

// Dừng một cron job
router.post('/cron/stop/:jobName', adminAuth, asyncHandler(async (req, res) => {
  const { jobName } = req.params;
  
  cronService.stopJob(jobName);
  res.json({
    success: true,
    message: `Job '${jobName}' stopped`
  });
}));

// Khởi động lại một cron job
router.post('/cron/restart/:jobName', adminAuth, asyncHandler(async (req, res) => {
  const { jobName } = req.params;
  
  cronService.restartJob(jobName);
  res.json({
    success: true,
    message: `Job '${jobName}' restarted`
  });
}));

// Test gửi email nhắc nhở
router.post('/email/test-reminder', adminAuth, asyncHandler(async (req, res) => {
  const { userId, taskId } = req.body;

  if (!userId || !taskId) {
    return res.status(400).json({
      success: false,
      message: 'userId and taskId are required'
    });
  }

  const user = await User.findById(userId);
  const task = await Todo.findById(taskId);

  if (!user || !task) {
    return res.status(404).json({
      success: false,
      message: 'User or task not found'
    });
  }

  try {
    const emailSent = await emailService.sendTaskReminderEmail(user, task);
    
    res.json({
      success: true,
      message: emailSent ? 'Test reminder email sent successfully' : 'Failed to send email',
      emailSent
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: `Failed to send test email: ${error.message}`
    });
  }
}));

// Test gửi email quá hạn
router.post('/email/test-overdue', adminAuth, asyncHandler(async (req, res) => {
  const { userId, taskId } = req.body;

  if (!userId || !taskId) {
    return res.status(400).json({
      success: false,
      message: 'userId and taskId are required'
    });
  }

  const user = await User.findById(userId);
  const task = await Todo.findById(taskId);

  if (!user || !task) {
    return res.status(404).json({
      success: false,
      message: 'User or task not found'
    });
  }

  try {
    const emailSent = await emailService.sendTaskOverdueEmail(user, task);
    
    res.json({
      success: true,
      message: emailSent ? 'Test overdue email sent successfully' : 'Failed to send email',
      emailSent
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: `Failed to send test email: ${error.message}`
    });
  }
}));

// Test gửi báo cáo hàng ngày
router.post('/email/test-digest', adminAuth, asyncHandler(async (req, res) => {
  const { userId } = req.body;

  if (!userId) {
    return res.status(400).json({
      success: false,
      message: 'userId is required'
    });
  }

  const user = await User.findById(userId);
  if (!user) {
    return res.status(404).json({
      success: false,
      message: 'User not found'
    });
  }

  // Lấy tasks của user
  const tasks = await Todo.find({ user: userId });

  try {
    const emailSent = await emailService.sendDailyDigestEmail(user, tasks);
    
    res.json({
      success: true,
      message: emailSent ? 'Test daily digest email sent successfully' : 'Failed to send email',
      emailSent,
      tasksCount: tasks.length
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: `Failed to send test email: ${error.message}`
    });
  }
}));

// Lấy thống kê email
router.get('/email/stats', adminAuth, asyncHandler(async (req, res) => {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);

  // Thống kê tasks cần nhắc nhở hôm nay
  const tasksToRemindToday = await Todo.count({
    where: {
      deadline: { [Op.gte]: today, [Op.lt]: tomorrow },
      isDone: false,
      remindAt: { [Op.lte]: now }
    }
  });

  // Thống kê tasks quá hạn
  const overdueTasks = await Todo.count({
    where: {
      deadline: { [Op.lt]: now },
      isDone: false
    }
  });

  // Thống kê users có tasks
  const usersWithTasks = await User.count({
    include: [{
      model: Todo,
      as: 'todos',
      required: true
    }]
  });

  // Tasks được tạo hôm nay
  const tasksCreatedToday = await Todo.count({
    where: {
      createdAt: { [Op.gte]: today, [Op.lt]: tomorrow }
    }
  });

  // Tasks hoàn thành hôm nay
  const tasksCompletedToday = await Todo.count({
    where: {
      completedAt: { [Op.gte]: today, [Op.lt]: tomorrow }
    }
  });

  res.json({
    success: true,
    data: {
      tasksToRemindToday,
      overdueTasks,
      usersWithTasks,
      tasksCreatedToday,
      tasksCompletedToday,
      timestamp: now
    }
  });
}));

// Cấu hình email settings
router.get('/email/config', adminAuth, asyncHandler(async (req, res) => {
  res.json({
    success: true,
    data: {
      emailService: process.env.EMAIL_SERVICE || 'gmail',
      emailUser: process.env.EMAIL_USER || 'not configured',
      frontendUrl: process.env.FRONTEND_URL || 'http://localhost:3000',
      nodeEnv: process.env.NODE_ENV || 'development',
      emailConfigured: !!(process.env.EMAIL_USER && process.env.EMAIL_PASSWORD)
    }
  });
}));

// Cập nhật cấu hình email (chỉ trong development)
router.post('/email/config', adminAuth, asyncHandler(async (req, res) => {
  if (process.env.NODE_ENV === 'production') {
    return res.status(403).json({
      success: false,
      message: 'Email configuration cannot be changed in production'
    });
  }

  const { emailService, emailUser, emailPassword, frontendUrl } = req.body;

  // Cập nhật environment variables (chỉ trong session hiện tại)
  if (emailService) process.env.EMAIL_SERVICE = emailService;
  if (emailUser) process.env.EMAIL_USER = emailUser;
  if (emailPassword) process.env.EMAIL_PASSWORD = emailPassword;
  if (frontendUrl) process.env.FRONTEND_URL = frontendUrl;

  // Khởi tạo lại email service
  await emailService.initializeTransporter();

  res.json({
    success: true,
    message: 'Email configuration updated (session only)',
    data: {
      emailService: process.env.EMAIL_SERVICE,
      emailUser: process.env.EMAIL_USER,
      frontendUrl: process.env.FRONTEND_URL
    }
  });
}));

module.exports = router;
