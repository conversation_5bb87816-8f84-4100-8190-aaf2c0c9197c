'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useAuth } from '@/lib/auth-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import {
  Users,
  BookOpen,
  CheckSquare,
  Calendar,
  TrendingUp,
  Target,
  Clock,
  Award,
  Brain,
  Zap,
  RefreshCw,
  Plus,
  Filter,
  Search,
  Bell,
  Settings,
  LogOut
} from 'lucide-react';
import { useRealTimeStats, useRealTimeTodos, useRealTimeUsers, useConnectionStatus } from '@/hooks/useRealTimeData';
import { RealTimeCollaboration } from '@/components/features/real-time-collaboration';
import { RealTimeNotifications } from '@/components/features/real-time-notifications';
import { ErrorBoundary } from '@/components/ui/error-boundary';

// Dashboard Header Component
function DashboardHeader({ user, onRefresh, isRefreshing }: any) {
  return (
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
      <div>
        <motion.h1 
          className="text-3xl font-bold text-gray-900"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          Welcome back, {user?.name || 'Student'}! 👋
        </motion.h1>
        <motion.p 
          className="text-gray-600 mt-2"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          Here's what's happening with your learning journey today.
        </motion.p>
      </div>
      
      <div className="flex items-center space-x-3">
        <RealTimeNotifications />
        
        <Button
          variant="outline"
          onClick={onRefresh}
          disabled={isRefreshing}
          className="flex items-center space-x-2"
        >
          <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
          <span>{isRefreshing ? 'Refreshing...' : 'Refresh'}</span>
        </Button>
        
        <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
          <Plus className="h-4 w-4 mr-2" />
          New Task
        </Button>
      </div>
    </div>
  );
}

// Stats Cards Component
function StatsCards({ stats, loading }: any) {
  const statsData = [
    {
      title: "Total Tasks",
      value: stats?.todos?.total || 0,
      change: "+12%",
      changeType: "positive",
      icon: <CheckSquare className="h-5 w-5" />,
      color: "blue"
    },
    {
      title: "Completed",
      value: stats?.todos?.completed || 0,
      change: "+8%",
      changeType: "positive", 
      icon: <Target className="h-5 w-5" />,
      color: "green"
    },
    {
      title: "In Progress",
      value: stats?.todos?.pending || 0,
      change: "-3%",
      changeType: "negative",
      icon: <Clock className="h-5 w-5" />,
      color: "yellow"
    },
    {
      title: "Productivity",
      value: `${stats?.productivity?.score || 0}%`,
      change: "+15%",
      changeType: "positive",
      icon: <TrendingUp className="h-5 w-5" />,
      color: "purple"
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {statsData.map((stat, index) => (
        <motion.div
          key={stat.title}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: index * 0.1 }}
        >
          <Card className="hover:shadow-lg transition-shadow duration-300">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">
                    {stat.title}
                  </p>
                  <p className="text-3xl font-bold text-gray-900">
                    {loading ? (
                      <div className="animate-pulse bg-gray-200 h-8 w-16 rounded"></div>
                    ) : (
                      stat.value
                    )}
                  </p>
                  <div className="flex items-center mt-2">
                    <span className={`text-sm font-medium ${
                      stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {stat.change}
                    </span>
                    <span className="text-sm text-gray-500 ml-1">vs last week</span>
                  </div>
                </div>
                <div className={`p-3 rounded-full bg-${stat.color}-100 text-${stat.color}-600`}>
                  {stat.icon}
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      ))}
    </div>
  );
}

// Recent Activity Component
function RecentActivity({ todos, loading }: any) {
  const recentTodos = todos?.slice(0, 5) || [];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Clock className="h-5 w-5 mr-2" />
          Recent Activity
        </CardTitle>
        <CardDescription>
          Your latest tasks and achievements
        </CardDescription>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="space-y-4">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="animate-pulse flex items-center space-x-3">
                <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        ) : recentTodos.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <CheckSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No recent activity</p>
            <p className="text-sm">Start by creating your first task!</p>
          </div>
        ) : (
          <div className="space-y-4">
            {recentTodos.map((todo: any, index: number) => (
              <motion.div
                key={todo.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                  todo.isDone ? 'bg-green-100 text-green-600' : 'bg-blue-100 text-blue-600'
                }`}>
                  {todo.isDone ? <CheckSquare className="h-5 w-5" /> : <Clock className="h-5 w-5" />}
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">{todo.title}</h4>
                  <p className="text-sm text-gray-500">
                    {todo.category} • {new Date(todo.createdAt).toLocaleDateString()}
                  </p>
                </div>
                <Badge variant={todo.isDone ? "default" : "secondary"}>
                  {todo.isDone ? "Completed" : "Pending"}
                </Badge>
              </motion.div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Progress Chart Component
function ProgressChart({ stats }: any) {
  const chartData = [
    { name: 'Mon', completed: 12, total: 15 },
    { name: 'Tue', completed: 19, total: 22 },
    { name: 'Wed', completed: 8, total: 12 },
    { name: 'Thu', completed: 15, total: 18 },
    { name: 'Fri', completed: 22, total: 25 },
    { name: 'Sat', completed: 18, total: 20 },
    { name: 'Sun', completed: 14, total: 16 },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <BarChart className="h-5 w-5 mr-2" />
          Weekly Progress
        </CardTitle>
        <CardDescription>
          Your task completion over the past week
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Bar dataKey="completed" fill="#3B82F6" radius={[4, 4, 0, 0]} />
              <Bar dataKey="total" fill="#E5E7EB" radius={[4, 4, 0, 0]} />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}

export default function CompleteDashboardPage() {
  const { user, isAuthenticated } = useAuth();
  
  // Real-time data hooks
  const { data: stats, loading: statsLoading, error: statsError, refresh: refreshStats } = useRealTimeStats();
  const { data: todos, loading: todosLoading, error: todosError } = useRealTimeTodos(user?.id);
  const { isOnline, apiStatus } = useConnectionStatus();
  
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await refreshStats();
    setTimeout(() => setIsRefreshing(false), 1000);
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-8 text-center">
            <h2 className="text-2xl font-bold mb-4">Access Denied</h2>
            <p className="text-gray-600 mb-6">Please sign in to access your dashboard.</p>
            <Button asChild>
              <a href="/login">Sign In</a>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <DashboardHeader 
            user={user} 
            onRefresh={handleRefresh} 
            isRefreshing={isRefreshing} 
          />

          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="tasks">Tasks</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
              <TabsTrigger value="collaboration">Collaboration</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              <StatsCards stats={stats} loading={statsLoading} />
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <RecentActivity todos={todos} loading={todosLoading} />
                <ProgressChart stats={stats} />
              </div>
            </TabsContent>

            <TabsContent value="tasks">
              <Card>
                <CardHeader>
                  <CardTitle>Task Management</CardTitle>
                  <CardDescription>Manage your tasks and assignments</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-center text-gray-500 py-8">
                    Task management interface will be implemented here
                  </p>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="analytics">
              <Card>
                <CardHeader>
                  <CardTitle>Learning Analytics</CardTitle>
                  <CardDescription>Detailed insights into your learning progress</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-center text-gray-500 py-8">
                    Advanced analytics dashboard will be implemented here
                  </p>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="collaboration">
              <RealTimeCollaboration 
                roomId={`user_${user?.id}`}
                roomType="study_group"
                title="My Study Space"
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </ErrorBoundary>
  );
}
