{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "9CYgLGHPHZbYUAZenmeP6FmVWQXW/LHxJQDgFuAOCXI=", "__NEXT_PREVIEW_MODE_ID": "51909c1ff4dbcea74b6840122f57f4fb", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4131bdfe2ecf9e7c047f8b2d515736ba1a8bf15efd6ce286a9740b7de8a3fec2", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "34456dc827a83220f855f118d0d59682a6a80217e6157446becceaccd5192934"}}}, "functions": {}, "sortedMiddleware": ["/"]}