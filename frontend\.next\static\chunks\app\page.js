/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cquang%5C%5CDesktop%5C%5Chackathon%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cquang%5C%5CDesktop%5C%5Chackathon%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(app-pages-browser)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDcXVhbmclNUMlNUNEZXNrdG9wJTVDJTVDaGFja2F0aG9uJTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLDhKQUF1RyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxccXVhbmdcXFxcRGVza3RvcFxcXFxoYWNrYXRob25cXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cquang%5C%5CDesktop%5C%5Chackathon%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CompleteHomePage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_ChevronDown_GraduationCap_Menu_Play_Star_Target_Trophy_Users_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,ChevronDown,GraduationCap,Menu,Play,Star,Target,Trophy,Users,WifiOff,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wifi-off.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_ChevronDown_GraduationCap_Menu_Play_Star_Target_Trophy_Users_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,ChevronDown,GraduationCap,Menu,Play,Star,Target,Trophy,Users,WifiOff,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/graduation-cap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_ChevronDown_GraduationCap_Menu_Play_Star_Target_Trophy_Users_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,ChevronDown,GraduationCap,Menu,Play,Star,Target,Trophy,Users,WifiOff,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_ChevronDown_GraduationCap_Menu_Play_Star_Target_Trophy_Users_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,ChevronDown,GraduationCap,Menu,Play,Star,Target,Trophy,Users,WifiOff,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_ChevronDown_GraduationCap_Menu_Play_Star_Target_Trophy_Users_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,ChevronDown,GraduationCap,Menu,Play,Star,Target,Trophy,Users,WifiOff,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_ChevronDown_GraduationCap_Menu_Play_Star_Target_Trophy_Users_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,ChevronDown,GraduationCap,Menu,Play,Star,Target,Trophy,Users,WifiOff,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_ChevronDown_GraduationCap_Menu_Play_Star_Target_Trophy_Users_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,ChevronDown,GraduationCap,Menu,Play,Star,Target,Trophy,Users,WifiOff,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_ChevronDown_GraduationCap_Menu_Play_Star_Target_Trophy_Users_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,ChevronDown,GraduationCap,Menu,Play,Star,Target,Trophy,Users,WifiOff,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_ChevronDown_GraduationCap_Menu_Play_Star_Target_Trophy_Users_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,ChevronDown,GraduationCap,Menu,Play,Star,Target,Trophy,Users,WifiOff,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_ChevronDown_GraduationCap_Menu_Play_Star_Target_Trophy_Users_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,ChevronDown,GraduationCap,Menu,Play,Star,Target,Trophy,Users,WifiOff,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_ChevronDown_GraduationCap_Menu_Play_Star_Target_Trophy_Users_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,ChevronDown,GraduationCap,Menu,Play,Star,Target,Trophy,Users,WifiOff,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_BookOpen_ChevronDown_GraduationCap_Menu_Play_Star_Target_Trophy_Users_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,BookOpen,ChevronDown,GraduationCap,Menu,Play,Star,Target,Trophy,Users,WifiOff,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _hooks_useRealTimeData__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useRealTimeData */ \"(app-pages-browser)/./src/hooks/useRealTimeData.ts\");\n/* harmony import */ var _components_features_real_time_notifications__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/features/real-time-notifications */ \"(app-pages-browser)/./src/components/features/real-time-notifications.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n// Professional Header Component\nfunction ProfessionalHeader() {\n    _s();\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isScrolled, setIsScrolled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { isOnline, apiStatus } = (0,_hooks_useRealTimeData__WEBPACK_IMPORTED_MODULE_4__.useConnectionStatus)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProfessionalHeader.useEffect\": ()=>{\n            const handleScroll = {\n                \"ProfessionalHeader.useEffect.handleScroll\": ()=>{\n                    setIsScrolled(window.scrollY > 50);\n                }\n            }[\"ProfessionalHeader.useEffect.handleScroll\"];\n            window.addEventListener('scroll', handleScroll);\n            return ({\n                \"ProfessionalHeader.useEffect\": ()=>window.removeEventListener('scroll', handleScroll)\n            })[\"ProfessionalHeader.useEffect\"];\n        }\n    }[\"ProfessionalHeader.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.header, {\n        className: \"fixed top-0 left-0 right-0 z-50 transition-all duration-300 \".concat(isScrolled ? 'bg-white/95 backdrop-blur-md shadow-lg border-b' : 'bg-transparent'),\n        initial: {\n            y: -100\n        },\n        animate: {\n            y: 0\n        },\n        transition: {\n            duration: 0.6\n        },\n        children: [\n            (!isOnline || apiStatus === 'disconnected') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-500 text-white text-center py-2 text-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_ChevronDown_GraduationCap_Menu_Play_Star_Target_Trophy_Users_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: !isOnline ? 'No internet connection' : 'Server disconnected'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 61,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-16 items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                        className: \"h-10 w-10 rounded-xl bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center\",\n                                        whileHover: {\n                                            scale: 1.05,\n                                            rotate: 5\n                                        },\n                                        whileTap: {\n                                            scale: 0.95\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_ChevronDown_GraduationCap_Menu_Play_Star_Target_Trophy_Users_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-6 w-6 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n                                                children: \"FPT UniHub\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 rounded-full \".concat(apiStatus === 'connected' && isOnline ? 'bg-green-500 animate-pulse' : 'bg-red-500')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 85,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: apiStatus === 'connected' && isOnline ? 'Live' : 'Offline'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 86,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"hidden md:flex items-center space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/features\",\n                                        className: \"text-gray-600 hover:text-blue-600 transition-colors font-medium\",\n                                        children: \"Features\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/pricing\",\n                                        className: \"text-gray-600 hover:text-blue-600 transition-colors font-medium\",\n                                        children: \"Pricing\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/about\",\n                                        className: \"text-gray-600 hover:text-blue-600 transition-colors font-medium\",\n                                        children: \"About\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/contact\",\n                                        className: \"text-gray-600 hover:text-blue-600 transition-colors font-medium\",\n                                        children: \"Contact\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_features_real_time_notifications__WEBPACK_IMPORTED_MODULE_5__.RealTimeNotifications, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden md:flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/login\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    className: \"font-medium\",\n                                                    children: \"Sign In\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/register\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    className: \"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 font-medium\",\n                                                    children: \"Get Started\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"md:hidden\",\n                                        onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                                        children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_ChevronDown_GraduationCap_Menu_Play_Star_Target_Trophy_Users_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 29\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_ChevronDown_GraduationCap_Menu_Play_Star_Target_Trophy_Users_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 57\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                        children: isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                height: 0\n                            },\n                            animate: {\n                                opacity: 1,\n                                height: 'auto'\n                            },\n                            exit: {\n                                opacity: 0,\n                                height: 0\n                            },\n                            className: \"md:hidden border-t bg-white/95 backdrop-blur-md\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"py-4 space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/features\",\n                                        className: \"block px-4 py-2 text-gray-600 hover:text-blue-600 transition-colors\",\n                                        children: \"Features\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/pricing\",\n                                        className: \"block px-4 py-2 text-gray-600 hover:text-blue-600 transition-colors\",\n                                        children: \"Pricing\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/about\",\n                                        className: \"block px-4 py-2 text-gray-600 hover:text-blue-600 transition-colors\",\n                                        children: \"About\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/contact\",\n                                        className: \"block px-4 py-2 text-gray-600 hover:text-blue-600 transition-colors\",\n                                        children: \"Contact\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-4 pt-4 border-t space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/login\",\n                                                className: \"block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    className: \"w-full justify-start\",\n                                                    children: \"Sign In\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/register\",\n                                                className: \"block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    className: \"w-full bg-gradient-to-r from-blue-600 to-purple-600\",\n                                                    children: \"Get Started\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n_s(ProfessionalHeader, \"MiYc4QSuFvnWLhUpQ8JUB0+3rvI=\", false, function() {\n    return [\n        _hooks_useRealTimeData__WEBPACK_IMPORTED_MODULE_4__.useConnectionStatus\n    ];\n});\n_c = ProfessionalHeader;\n// Hero Section Component\nfunction HeroSection() {\n    _s1();\n    const [currentFeature, setCurrentFeature] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const features = [\n        \"AI-Powered Learning Assistant\",\n        \"Smart Todo Management\",\n        \"Real-time Collaboration\",\n        \"Progress Analytics\"\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"HeroSection.useEffect\": ()=>{\n            const interval = setInterval({\n                \"HeroSection.useEffect.interval\": ()=>{\n                    setCurrentFeature({\n                        \"HeroSection.useEffect.interval\": (prev)=>(prev + 1) % features.length\n                    }[\"HeroSection.useEffect.interval\"]);\n                }\n            }[\"HeroSection.useEffect.interval\"], 3000);\n            return ({\n                \"HeroSection.useEffect\": ()=>clearInterval(interval)\n            })[\"HeroSection.useEffect\"];\n        }\n    }[\"HeroSection.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-blue-50 via-white to-purple-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-20 left-10 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-40 right-10 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -bottom-8 left-20 w-72 h-72 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 relative z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8\n                            },\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-5xl md:text-7xl font-bold mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent\",\n                                            children: \"Transform Your\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-900\",\n                                            children: \"Learning Journey\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 222,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-8 mb-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.AnimatePresence, {\n                                        mode: \"wait\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.p, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                y: -20\n                                            },\n                                            transition: {\n                                                duration: 0.5\n                                            },\n                                            className: \"text-xl md:text-2xl text-gray-600 font-medium\",\n                                            children: [\n                                                \"with \",\n                                                features[currentFeature]\n                                            ]\n                                        }, currentFeature, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.p, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.2\n                            },\n                            className: \"text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed\",\n                            children: \"Join thousands of FPT University students using our AI-powered platform for personalized learning, smart task management, and collaborative study experiences.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.4\n                            },\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center items-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/register\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        size: \"lg\",\n                                        className: \"bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 text-lg font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300\",\n                                        children: [\n                                            \"Start Learning Free\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_ChevronDown_GraduationCap_Menu_Play_Star_Target_Trophy_Users_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"ml-2 h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    size: \"lg\",\n                                    className: \"px-8 py-4 text-lg font-semibold rounded-xl border-2 hover:bg-gray-50 transition-all duration-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_ChevronDown_GraduationCap_Menu_Play_Star_Target_Trophy_Users_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"mr-2 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Watch Demo\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 30\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            transition: {\n                                duration: 0.8,\n                                delay: 0.6\n                            },\n                            className: \"flex flex-wrap justify-center items-center gap-8 text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_ChevronDown_GraduationCap_Menu_Play_Star_Target_Trophy_Users_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-5 w-5 text-yellow-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"Award Winning Platform\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_ChevronDown_GraduationCap_Menu_Play_Star_Target_Trophy_Users_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-5 w-5 text-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"10,000+ Active Students\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 290,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_ChevronDown_GraduationCap_Menu_Play_Star_Target_Trophy_Users_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-5 w-5 text-yellow-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"4.9/5 Rating\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 208,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                transition: {\n                    delay: 1,\n                    duration: 0.8\n                },\n                className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    animate: {\n                        y: [\n                            0,\n                            10,\n                            0\n                        ]\n                    },\n                    transition: {\n                        duration: 2,\n                        repeat: Infinity\n                    },\n                    className: \"flex flex-col items-center text-gray-400\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm mb-2\",\n                            children: \"Scroll to explore\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_ChevronDown_GraduationCap_Menu_Play_Star_Target_Trophy_Users_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 303,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 200,\n        columnNumber: 5\n    }, this);\n}\n_s1(HeroSection, \"mlsM0SPMGMIvzZAtxL2CKvB3M04=\");\n_c1 = HeroSection;\nfunction CompleteHomePage() {\n    _s2();\n    // Real-time data hooks\n    const { data: stats, loading: statsLoading, error: statsError, refresh: refreshStats } = (0,_hooks_useRealTimeData__WEBPACK_IMPORTED_MODULE_4__.useRealTimeStats)();\n    const { data: testimonials, loading: testimonialsLoading } = (0,_hooks_useRealTimeData__WEBPACK_IMPORTED_MODULE_4__.useRealTimeTestimonials)();\n    const { isOnline, apiStatus } = (0,_hooks_useRealTimeData__WEBPACK_IMPORTED_MODULE_4__.useConnectionStatus)();\n    // Process stats for display\n    const displayStats = (stats === null || stats === void 0 ? void 0 : stats.displayStats) || {\n        activeStudents: statsLoading ? \"Loading...\" : \"10k+\",\n        coursesAvailable: \"500+\",\n        successRate: statsLoading ? \"Loading...\" : \"95%\",\n        goalsAchieved: statsLoading ? \"Loading...\" : \"50k+\"\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProfessionalHeader, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 338,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HeroSection, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold mb-8\",\n                            children: \"Real-time Statistics\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-8\",\n                            children: [\n                                {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_ChevronDown_GraduationCap_Menu_Play_Star_Target_Trophy_Users_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 23\n                                    }, this),\n                                    label: \"Active Students\",\n                                    value: displayStats.activeStudents\n                                },\n                                {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_ChevronDown_GraduationCap_Menu_Play_Star_Target_Trophy_Users_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 23\n                                    }, this),\n                                    label: \"Courses Available\",\n                                    value: displayStats.coursesAvailable\n                                },\n                                {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_ChevronDown_GraduationCap_Menu_Play_Star_Target_Trophy_Users_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 23\n                                    }, this),\n                                    label: \"Success Rate\",\n                                    value: displayStats.successRate\n                                },\n                                {\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_BookOpen_ChevronDown_GraduationCap_Menu_Play_Star_Target_Trophy_Users_WifiOff_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 23\n                                    }, this),\n                                    label: \"Goals Achieved\",\n                                    value: displayStats.goalsAchieved\n                                }\n                            ].map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    className: \"text-center\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 30\n                                    },\n                                    whileInView: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    viewport: {\n                                        once: true\n                                    },\n                                    transition: {\n                                        delay: index * 0.1,\n                                        duration: 0.5\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full text-blue-600\",\n                                                children: stat.icon\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-4xl font-bold text-gray-900 mb-2\",\n                                            children: statsLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-pulse bg-gray-200 h-10 w-20 mx-auto rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 367,\n                                                columnNumber: 21\n                                            }, this) : stat.value\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600 font-medium\",\n                                            children: stat.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 343,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 342,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 337,\n        columnNumber: 5\n    }, this);\n}\n_s2(CompleteHomePage, \"PBkYDjxD5+pADj0R/ppvPXNrcCE=\", false, function() {\n    return [\n        _hooks_useRealTimeData__WEBPACK_IMPORTED_MODULE_4__.useRealTimeStats,\n        _hooks_useRealTimeData__WEBPACK_IMPORTED_MODULE_4__.useRealTimeTestimonials,\n        _hooks_useRealTimeData__WEBPACK_IMPORTED_MODULE_4__.useConnectionStatus\n    ];\n});\n_c2 = CompleteHomePage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ProfessionalHeader\");\n$RefreshReg$(_c1, \"HeroSection\");\n$RefreshReg$(_c2, \"CompleteHomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/features/real-time-notifications.tsx":
/*!*************************************************************!*\
  !*** ./src/components/features/real-time-notifications.tsx ***!
  \*************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RealTimeNotifications: () => (/* binding */ RealTimeNotifications)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _lib_websocket_service__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/websocket-service */ \"(app-pages-browser)/./src/lib/websocket-service.ts\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_BookOpen_CheckCircle_Clock_Info_MessageSquare_Trophy_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,BookOpen,CheckCircle,Clock,Info,MessageSquare,Trophy,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_BookOpen_CheckCircle_Clock_Info_MessageSquare_Trophy_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,BookOpen,CheckCircle,Clock,Info,MessageSquare,Trophy,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_BookOpen_CheckCircle_Clock_Info_MessageSquare_Trophy_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,BookOpen,CheckCircle,Clock,Info,MessageSquare,Trophy,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_BookOpen_CheckCircle_Clock_Info_MessageSquare_Trophy_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,BookOpen,CheckCircle,Clock,Info,MessageSquare,Trophy,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_BookOpen_CheckCircle_Clock_Info_MessageSquare_Trophy_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,BookOpen,CheckCircle,Clock,Info,MessageSquare,Trophy,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_BookOpen_CheckCircle_Clock_Info_MessageSquare_Trophy_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,BookOpen,CheckCircle,Clock,Info,MessageSquare,Trophy,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_BookOpen_CheckCircle_Clock_Info_MessageSquare_Trophy_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,BookOpen,CheckCircle,Clock,Info,MessageSquare,Trophy,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_BookOpen_CheckCircle_Clock_Info_MessageSquare_Trophy_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,BookOpen,CheckCircle,Clock,Info,MessageSquare,Trophy,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_BookOpen_CheckCircle_Clock_Info_MessageSquare_Trophy_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,BookOpen,CheckCircle,Clock,Info,MessageSquare,Trophy,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Bell_BookOpen_CheckCircle_Clock_Info_MessageSquare_Trophy_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Bell,BookOpen,CheckCircle,Clock,Info,MessageSquare,Trophy,X,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/**\n * Real-time Notification System\n * Provides live notifications, alerts, and updates\n */ /* __next_internal_client_entry_do_not_use__ RealTimeNotifications auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction RealTimeNotifications(param) {\n    let { maxNotifications = 50, showToasts = true, autoMarkAsRead = false } = param;\n    _s();\n    // const { user } = useAuth();\n    const user = null; // Temporary fix\n    const { isConnected, send, subscribe } = (0,_lib_websocket_service__WEBPACK_IMPORTED_MODULE_2__.useWebSocket)();\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showPanel, setShowPanel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RealTimeNotifications.useEffect\": ()=>{\n            if (!isConnected || !user) return;\n            // Subscribe to notifications\n            const unsubscribeNotifications = subscribe('notification', {\n                \"RealTimeNotifications.useEffect.unsubscribeNotifications\": (data)=>{\n                    const notification = {\n                        id: data.id || \"notif_\".concat(Date.now()),\n                        type: data.type || 'info',\n                        title: data.title,\n                        message: data.message,\n                        timestamp: new Date(data.timestamp || Date.now()),\n                        read: false,\n                        actionUrl: data.actionUrl,\n                        actionLabel: data.actionLabel,\n                        userId: data.userId,\n                        userName: data.userName,\n                        userAvatar: data.userAvatar,\n                        priority: data.priority || 'medium',\n                        category: data.category || 'system'\n                    };\n                    setNotifications({\n                        \"RealTimeNotifications.useEffect.unsubscribeNotifications\": (prev)=>{\n                            const updated = [\n                                notification,\n                                ...prev\n                            ].slice(0, maxNotifications);\n                            return updated;\n                        }\n                    }[\"RealTimeNotifications.useEffect.unsubscribeNotifications\"]);\n                    // Show toast if enabled\n                    if (showToasts) {\n                        showNotificationToast(notification);\n                    }\n                    // Auto mark as read if enabled\n                    if (autoMarkAsRead) {\n                        setTimeout({\n                            \"RealTimeNotifications.useEffect.unsubscribeNotifications\": ()=>{\n                                markAsRead(notification.id);\n                            }\n                        }[\"RealTimeNotifications.useEffect.unsubscribeNotifications\"], 3000);\n                    }\n                }\n            }[\"RealTimeNotifications.useEffect.unsubscribeNotifications\"]);\n            // Request initial notifications\n            send('get_notifications', {\n                userId: user.id\n            });\n            return ({\n                \"RealTimeNotifications.useEffect\": ()=>{\n                    unsubscribeNotifications();\n                }\n            })[\"RealTimeNotifications.useEffect\"];\n        }\n    }[\"RealTimeNotifications.useEffect\"], [\n        isConnected,\n        user,\n        maxNotifications,\n        showToasts,\n        autoMarkAsRead,\n        send,\n        subscribe\n    ]);\n    const showNotificationToast = (notification)=>{\n        const toastOptions = {\n            duration: notification.priority === 'urgent' ? 8000 : 4000,\n            position: 'top-right'\n        };\n        switch(notification.type){\n            case 'success':\n            case 'achievement':\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.success(notification.message, toastOptions);\n                break;\n            case 'error':\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast.error(notification.message, toastOptions);\n                break;\n            case 'warning':\n                (0,react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast)(notification.message, {\n                    ...toastOptions,\n                    icon: '⚠️'\n                });\n                break;\n            default:\n                (0,react_hot_toast__WEBPACK_IMPORTED_MODULE_6__.toast)(notification.message, toastOptions);\n        }\n    };\n    const markAsRead = (notificationId)=>{\n        setNotifications((prev)=>prev.map((notif)=>notif.id === notificationId ? {\n                    ...notif,\n                    read: true\n                } : notif));\n        send('mark_notification_read', {\n            notificationId\n        });\n    };\n    const markAllAsRead = ()=>{\n        setNotifications((prev)=>prev.map((notif)=>({\n                    ...notif,\n                    read: true\n                })));\n        send('mark_all_notifications_read', {\n            userId: user === null || user === void 0 ? void 0 : user.id\n        });\n    };\n    const deleteNotification = (notificationId)=>{\n        setNotifications((prev)=>prev.filter((notif)=>notif.id !== notificationId));\n        send('delete_notification', {\n            notificationId\n        });\n    };\n    const getNotificationIcon = (type)=>{\n        switch(type){\n            case 'success':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_BookOpen_CheckCircle_Clock_Info_MessageSquare_Trophy_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-5 w-5 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 30\n                }, this);\n            case 'error':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_BookOpen_CheckCircle_Clock_Info_MessageSquare_Trophy_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-5 w-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 28\n                }, this);\n            case 'warning':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_BookOpen_CheckCircle_Clock_Info_MessageSquare_Trophy_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-5 w-5 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 30\n                }, this);\n            case 'assignment':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_BookOpen_CheckCircle_Clock_Info_MessageSquare_Trophy_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-5 w-5 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                    lineNumber: 169,\n                    columnNumber: 33\n                }, this);\n            case 'message':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_BookOpen_CheckCircle_Clock_Info_MessageSquare_Trophy_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-5 w-5 text-purple-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 30\n                }, this);\n            case 'achievement':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_BookOpen_CheckCircle_Clock_Info_MessageSquare_Trophy_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-5 w-5 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 34\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_BookOpen_CheckCircle_Clock_Info_MessageSquare_Trophy_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-5 w-5 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const getPriorityColor = (priority)=>{\n        switch(priority){\n            case 'urgent':\n                return 'border-l-red-500 bg-red-50';\n            case 'high':\n                return 'border-l-orange-500 bg-orange-50';\n            case 'medium':\n                return 'border-l-blue-500 bg-blue-50';\n            default:\n                return 'border-l-gray-500 bg-gray-50';\n        }\n    };\n    const filteredNotifications = notifications.filter((notif)=>{\n        switch(filter){\n            case 'unread':\n                return !notif.read;\n            case 'academic':\n                return notif.category === 'academic';\n            case 'social':\n                return notif.category === 'social';\n            default:\n                return true;\n        }\n    });\n    const unreadCount = notifications.filter((n)=>!n.read).length;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                variant: \"ghost\",\n                size: \"icon\",\n                onClick: ()=>setShowPanel(!showPanel),\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_BookOpen_CheckCircle_Clock_Info_MessageSquare_Trophy_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"h-5 w-5\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this),\n                    unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                        initial: {\n                            scale: 0\n                        },\n                        animate: {\n                            scale: 1\n                        },\n                        className: \"absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\",\n                        children: unreadCount > 99 ? '99+' : unreadCount\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_16__.AnimatePresence, {\n                children: showPanel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        scale: 0.95,\n                        y: -10\n                    },\n                    animate: {\n                        opacity: 1,\n                        scale: 1,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        scale: 0.95,\n                        y: -10\n                    },\n                    transition: {\n                        duration: 0.2\n                    },\n                    className: \"absolute right-0 top-full mt-2 w-96 z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"shadow-xl border-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"pb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"text-lg\",\n                                                children: \"Notifications\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        onClick: markAllAsRead,\n                                                        className: \"text-xs\",\n                                                        children: \"Mark all read\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"icon\",\n                                                        onClick: ()=>setShowPanel(false),\n                                                        className: \"h-6 w-6\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_BookOpen_CheckCircle_Clock_Info_MessageSquare_Trophy_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1 mt-3\",\n                                        children: [\n                                            'all',\n                                            'unread',\n                                            'academic',\n                                            'social'\n                                        ].map((filterType)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: filter === filterType ? 'default' : 'ghost',\n                                                size: \"sm\",\n                                                onClick: ()=>setFilter(filterType),\n                                                className: \"text-xs capitalize\",\n                                                children: [\n                                                    filterType,\n                                                    filterType === 'unread' && unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        className: \"ml-1 h-4 w-4 p-0 text-xs\",\n                                                        children: unreadCount\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, filterType, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                className: \"p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-h-96 overflow-y-auto\",\n                                    children: filteredNotifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 text-center text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_BookOpen_CheckCircle_Clock_Info_MessageSquare_Trophy_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-8 w-8 mx-auto mb-2 opacity-50\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"No notifications\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 21\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1\",\n                                        children: filteredNotifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: -20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                exit: {\n                                                    opacity: 0,\n                                                    x: 20\n                                                },\n                                                className: \"p-4 border-l-4 hover:bg-gray-50 cursor-pointer transition-colors \".concat(getPriorityColor(notification.priority), \" \").concat(!notification.read ? 'bg-blue-50' : 'bg-white'),\n                                                onClick: ()=>!notification.read && markAsRead(notification.id),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0 mt-1\",\n                                                            children: getNotificationIcon(notification.type)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"text-sm font-medium \".concat(!notification.read ? 'text-gray-900' : 'text-gray-700'),\n                                                                            children: notification.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                                                                            lineNumber: 301,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-1\",\n                                                                            children: [\n                                                                                !notification.read && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                                                                                    lineNumber: 308,\n                                                                                    columnNumber: 37\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                    variant: \"ghost\",\n                                                                                    size: \"icon\",\n                                                                                    onClick: (e)=>{\n                                                                                        e.stopPropagation();\n                                                                                        deleteNotification(notification.id);\n                                                                                    },\n                                                                                    className: \"h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_BookOpen_CheckCircle_Clock_Info_MessageSquare_Trophy_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                        className: \"h-3 w-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                                                                                        lineNumber: 319,\n                                                                                        columnNumber: 37\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                                                                                    lineNumber: 310,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                                                                            lineNumber: 306,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                                                                    lineNumber: 300,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-600 mt-1\",\n                                                                    children: notification.message\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                                                                    lineNumber: 324,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between mt-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center space-x-2 text-xs text-gray-500\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Bell_BookOpen_CheckCircle_Clock_Info_MessageSquare_Trophy_X_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                    className: \"h-3 w-3\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                                                                                    lineNumber: 330,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    children: notification.timestamp.toLocaleTimeString()\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                                                                                    lineNumber: 331,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                                                                            lineNumber: 329,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        notification.actionUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            variant: \"outline\",\n                                                                            size: \"sm\",\n                                                                            className: \"text-xs\",\n                                                                            onClick: (e)=>{\n                                                                                e.stopPropagation();\n                                                                                window.location.href = notification.actionUrl;\n                                                                            },\n                                                                            children: notification.actionLabel || 'View'\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                                                                            lineNumber: 337,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                                                                    lineNumber: 328,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 27\n                                                }, this)\n                                            }, notification.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 25\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                    lineNumber: 220,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\features\\\\real-time-notifications.tsx\",\n        lineNumber: 197,\n        columnNumber: 5\n    }, this);\n}\n_s(RealTimeNotifications, \"q4XmbXx0d8CWOCwos9kpIAaJaIc=\", false, function() {\n    return [\n        _lib_websocket_service__WEBPACK_IMPORTED_MODULE_2__.useWebSocket\n    ];\n});\n_c = RealTimeNotifications;\nvar _c;\n$RefreshReg$(_c, \"RealTimeNotifications\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/features/real-time-notifications.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/badge.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/badge.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(app-pages-browser)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)('inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2', {\n    variants: {\n        variant: {\n            default: 'border-transparent bg-primary text-primary-foreground hover:bg-primary/80',\n            secondary: 'border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80',\n            destructive: 'border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80',\n            outline: 'text-foreground',\n            success: 'border-transparent bg-green-500 text-white hover:bg-green-600',\n            warning: 'border-transparent bg-yellow-500 text-white hover:bg-yellow-600',\n            info: 'border-transparent bg-blue-500 text-white hover:bg-blue-600'\n        }\n    },\n    defaultVariants: {\n        variant: 'default'\n    }\n});\nfunction Badge(param) {\n    let { className, variant, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\hackathon\\\\frontend\\\\src\\\\components\\\\ui\\\\badge.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n_c = Badge;\n\nvar _c;\n$RefreshReg$(_c, \"Badge\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/badge.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useRealTimeData.ts":
/*!**************************************!*\
  !*** ./src/hooks/useRealTimeData.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useConnectionStatus: () => (/* binding */ useConnectionStatus),\n/* harmony export */   useRealTimeData: () => (/* binding */ useRealTimeData),\n/* harmony export */   useRealTimeNotifications: () => (/* binding */ useRealTimeNotifications),\n/* harmony export */   useRealTimeStats: () => (/* binding */ useRealTimeStats),\n/* harmony export */   useRealTimeTestimonials: () => (/* binding */ useRealTimeTestimonials),\n/* harmony export */   useRealTimeTodos: () => (/* binding */ useRealTimeTodos),\n/* harmony export */   useRealTimeUsers: () => (/* binding */ useRealTimeUsers)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/**\n * Real-time data hook for FPT UniHub\n * Provides live data updates with WebSocket integration\n */ \n\nfunction useRealTimeData(fetchFunction) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { refreshInterval = 30000, enableWebSocket = false, retryAttempts = 3, retryDelay = 1000 } = options;\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        data: null,\n        loading: true,\n        error: null,\n        lastUpdated: null\n    });\n    const retryCountRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const wsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const fetchData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeData.useCallback[fetchData]\": async function() {\n            let showLoading = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n            if (showLoading) {\n                setState({\n                    \"useRealTimeData.useCallback[fetchData]\": (prev)=>({\n                            ...prev,\n                            loading: true,\n                            error: null\n                        })\n                }[\"useRealTimeData.useCallback[fetchData]\"]);\n            }\n            try {\n                const data = await fetchFunction();\n                setState({\n                    data,\n                    loading: false,\n                    error: null,\n                    lastUpdated: new Date()\n                });\n                retryCountRef.current = 0;\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n                console.error('Real-time data fetch error:', error);\n                if (retryCountRef.current < retryAttempts) {\n                    retryCountRef.current++;\n                    setTimeout({\n                        \"useRealTimeData.useCallback[fetchData]\": ()=>fetchData(false)\n                    }[\"useRealTimeData.useCallback[fetchData]\"], retryDelay * retryCountRef.current);\n                } else {\n                    setState({\n                        \"useRealTimeData.useCallback[fetchData]\": (prev)=>({\n                                ...prev,\n                                loading: false,\n                                error: errorMessage\n                            })\n                    }[\"useRealTimeData.useCallback[fetchData]\"]);\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_1__.toast.error(\"Failed to fetch data: \".concat(errorMessage));\n                }\n            }\n        }\n    }[\"useRealTimeData.useCallback[fetchData]\"], [\n        fetchFunction,\n        retryAttempts,\n        retryDelay\n    ]);\n    const setupWebSocket = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeData.useCallback[setupWebSocket]\": ()=>{\n            if (!enableWebSocket) return;\n            try {\n                const wsUrl = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:5000';\n                wsRef.current = new WebSocket(wsUrl);\n                wsRef.current.onopen = ({\n                    \"useRealTimeData.useCallback[setupWebSocket]\": ()=>{\n                        console.log('WebSocket connected for real-time updates');\n                    }\n                })[\"useRealTimeData.useCallback[setupWebSocket]\"];\n                wsRef.current.onmessage = ({\n                    \"useRealTimeData.useCallback[setupWebSocket]\": (event)=>{\n                        try {\n                            const message = JSON.parse(event.data);\n                            if (message.type === 'data_update') {\n                                fetchData(false); // Refresh data without showing loading\n                            }\n                        } catch (error) {\n                            console.error('WebSocket message parsing error:', error);\n                        }\n                    }\n                })[\"useRealTimeData.useCallback[setupWebSocket]\"];\n                wsRef.current.onclose = ({\n                    \"useRealTimeData.useCallback[setupWebSocket]\": ()=>{\n                        console.log('WebSocket disconnected, attempting to reconnect...');\n                        setTimeout(setupWebSocket, 5000); // Reconnect after 5 seconds\n                    }\n                })[\"useRealTimeData.useCallback[setupWebSocket]\"];\n                wsRef.current.onerror = ({\n                    \"useRealTimeData.useCallback[setupWebSocket]\": (error)=>{\n                        console.error('WebSocket error:', error);\n                    }\n                })[\"useRealTimeData.useCallback[setupWebSocket]\"];\n            } catch (error) {\n                console.error('WebSocket setup error:', error);\n            }\n        }\n    }[\"useRealTimeData.useCallback[setupWebSocket]\"], [\n        enableWebSocket,\n        fetchData\n    ]);\n    const refresh = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useRealTimeData.useCallback[refresh]\": ()=>{\n            fetchData(true);\n        }\n    }[\"useRealTimeData.useCallback[refresh]\"], [\n        fetchData\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useRealTimeData.useEffect\": ()=>{\n            // Initial data fetch\n            fetchData();\n            // Setup polling interval\n            if (refreshInterval > 0) {\n                intervalRef.current = setInterval({\n                    \"useRealTimeData.useEffect\": ()=>{\n                        fetchData(false); // Don't show loading for background updates\n                    }\n                }[\"useRealTimeData.useEffect\"], refreshInterval);\n            }\n            // Setup WebSocket if enabled\n            setupWebSocket();\n            // Cleanup\n            return ({\n                \"useRealTimeData.useEffect\": ()=>{\n                    if (intervalRef.current) {\n                        clearInterval(intervalRef.current);\n                    }\n                    if (wsRef.current) {\n                        wsRef.current.close();\n                    }\n                }\n            })[\"useRealTimeData.useEffect\"];\n        }\n    }[\"useRealTimeData.useEffect\"], [\n        fetchData,\n        refreshInterval,\n        setupWebSocket\n    ]);\n    return {\n        ...state,\n        refresh,\n        isStale: state.lastUpdated ? Date.now() - state.lastUpdated.getTime() > refreshInterval * 2 : false\n    };\n}\n// Specialized hooks for common data types\nfunction useRealTimeStats() {\n    return useRealTimeData({\n        \"useRealTimeStats.useRealTimeData\": async ()=>{\n            const response = await fetch('http://localhost:5000/api/dashboard/public-stats');\n            const data = await response.json();\n            if (!data.success) throw new Error(data.message || 'Failed to fetch stats');\n            return data.data;\n        }\n    }[\"useRealTimeStats.useRealTimeData\"], {\n        refreshInterval: 60000,\n        enableWebSocket: true\n    } // Update every minute\n    );\n}\nfunction useRealTimeTestimonials() {\n    return useRealTimeData({\n        \"useRealTimeTestimonials.useRealTimeData\": async ()=>{\n            const response = await fetch('http://localhost:5000/api/dashboard/public-testimonials');\n            const data = await response.json();\n            if (!data.success) throw new Error(data.message || 'Failed to fetch testimonials');\n            return data.data;\n        }\n    }[\"useRealTimeTestimonials.useRealTimeData\"], {\n        refreshInterval: 300000\n    } // Update every 5 minutes\n    );\n}\nfunction useRealTimeUsers() {\n    return useRealTimeData({\n        \"useRealTimeUsers.useRealTimeData\": async ()=>{\n            const response = await fetch('http://localhost:5000/api/users');\n            const data = await response.json();\n            if (!data.success) throw new Error(data.message || 'Failed to fetch users');\n            return data.data;\n        }\n    }[\"useRealTimeUsers.useRealTimeData\"], {\n        refreshInterval: 30000,\n        enableWebSocket: true\n    });\n}\nfunction useRealTimeTodos(userId) {\n    return useRealTimeData({\n        \"useRealTimeTodos.useRealTimeData\": async ()=>{\n            const url = userId ? \"http://localhost:5000/api/todo?userId=\".concat(userId) : 'http://localhost:5000/api/todo';\n            const response = await fetch(url);\n            const data = await response.json();\n            if (!data.success) throw new Error(data.message || 'Failed to fetch todos');\n            return data.data;\n        }\n    }[\"useRealTimeTodos.useRealTimeData\"], {\n        refreshInterval: 15000,\n        enableWebSocket: true\n    });\n}\nfunction useRealTimeNotifications() {\n    return useRealTimeData({\n        \"useRealTimeNotifications.useRealTimeData\": async ()=>{\n            const response = await fetch('http://localhost:5000/api/notification');\n            const data = await response.json();\n            if (!data.success) throw new Error(data.message || 'Failed to fetch notifications');\n            return data.data;\n        }\n    }[\"useRealTimeNotifications.useRealTimeData\"], {\n        refreshInterval: 10000,\n        enableWebSocket: true\n    });\n}\n// Connection status hook\nfunction useConnectionStatus() {\n    const [isOnline, setIsOnline] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(navigator.onLine);\n    const [apiStatus, setApiStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('checking');\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useConnectionStatus.useEffect\": ()=>{\n            const handleOnline = {\n                \"useConnectionStatus.useEffect.handleOnline\": ()=>setIsOnline(true)\n            }[\"useConnectionStatus.useEffect.handleOnline\"];\n            const handleOffline = {\n                \"useConnectionStatus.useEffect.handleOffline\": ()=>setIsOnline(false)\n            }[\"useConnectionStatus.useEffect.handleOffline\"];\n            window.addEventListener('online', handleOnline);\n            window.addEventListener('offline', handleOffline);\n            // Check API status\n            const checkApiStatus = {\n                \"useConnectionStatus.useEffect.checkApiStatus\": async ()=>{\n                    try {\n                        const response = await fetch('http://localhost:5000/api/health', {\n                            method: 'GET',\n                            timeout: 5000\n                        });\n                        setApiStatus(response.ok ? 'connected' : 'disconnected');\n                    } catch (e) {\n                        setApiStatus('disconnected');\n                    }\n                }\n            }[\"useConnectionStatus.useEffect.checkApiStatus\"];\n            checkApiStatus();\n            const interval = setInterval(checkApiStatus, 30000);\n            return ({\n                \"useConnectionStatus.useEffect\": ()=>{\n                    window.removeEventListener('online', handleOnline);\n                    window.removeEventListener('offline', handleOffline);\n                    clearInterval(interval);\n                }\n            })[\"useConnectionStatus.useEffect\"];\n        }\n    }[\"useConnectionStatus.useEffect\"], []);\n    return {\n        isOnline,\n        apiStatus\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useRealTimeData.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/websocket-service.ts":
/*!**************************************!*\
  !*** ./src/lib/websocket-service.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WebSocketService: () => (/* binding */ WebSocketService),\n/* harmony export */   useWebSocket: () => (/* binding */ useWebSocket),\n/* harmony export */   wsService: () => (/* binding */ wsService)\n/* harmony export */ });\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/**\n * WebSocket Service for Real-time Features\n * Provides live updates, notifications, and collaboration features\n */ \nclass WebSocketService {\n    connect() {\n        return new Promise((resolve, reject)=>{\n            var _this_ws;\n            if (((_this_ws = this.ws) === null || _this_ws === void 0 ? void 0 : _this_ws.readyState) === WebSocket.OPEN) {\n                resolve();\n                return;\n            }\n            if (this.isConnecting) {\n                return;\n            }\n            this.isConnecting = true;\n            this.isManualClose = false;\n            try {\n                this.ws = new WebSocket(this.config.url);\n                this.ws.onopen = ()=>{\n                    console.log('🔗 WebSocket connected');\n                    this.isConnecting = false;\n                    this.reconnectAttempts = 0;\n                    this.startHeartbeat();\n                    this.emit('connection', {\n                        status: 'connected'\n                    });\n                    resolve();\n                };\n                this.ws.onmessage = (event)=>{\n                    try {\n                        const message = JSON.parse(event.data);\n                        this.handleMessage(message);\n                    } catch (error) {\n                        console.error('Failed to parse WebSocket message:', error);\n                    }\n                };\n                this.ws.onclose = (event)=>{\n                    console.log('🔌 WebSocket disconnected:', event.code, event.reason);\n                    this.isConnecting = false;\n                    this.stopHeartbeat();\n                    this.emit('connection', {\n                        status: 'disconnected',\n                        code: event.code,\n                        reason: event.reason\n                    });\n                    if (!this.isManualClose && this.reconnectAttempts < this.config.maxReconnectAttempts) {\n                        this.scheduleReconnect();\n                    }\n                };\n                this.ws.onerror = (error)=>{\n                    console.error('❌ WebSocket error:', error);\n                    this.isConnecting = false;\n                    this.emit('error', {\n                        error\n                    });\n                    reject(error);\n                };\n            } catch (error) {\n                this.isConnecting = false;\n                reject(error);\n            }\n        });\n    }\n    disconnect() {\n        this.isManualClose = true;\n        this.stopHeartbeat();\n        this.clearReconnectTimer();\n        if (this.ws) {\n            this.ws.close(1000, 'Manual disconnect');\n            this.ws = null;\n        }\n    }\n    send(type, data) {\n        var _this_ws;\n        if (((_this_ws = this.ws) === null || _this_ws === void 0 ? void 0 : _this_ws.readyState) === WebSocket.OPEN) {\n            const message = {\n                type,\n                data,\n                timestamp: Date.now()\n            };\n            this.ws.send(JSON.stringify(message));\n        } else {\n            console.warn('WebSocket not connected, message not sent:', type, data);\n        }\n    }\n    subscribe(eventType, callback) {\n        if (!this.listeners.has(eventType)) {\n            this.listeners.set(eventType, new Set());\n        }\n        this.listeners.get(eventType).add(callback);\n        // Return unsubscribe function\n        return ()=>{\n            const callbacks = this.listeners.get(eventType);\n            if (callbacks) {\n                callbacks.delete(callback);\n                if (callbacks.size === 0) {\n                    this.listeners.delete(eventType);\n                }\n            }\n        };\n    }\n    handleMessage(message) {\n        const { type, data } = message;\n        switch(type){\n            case 'heartbeat':\n                this.send('heartbeat_ack', {});\n                break;\n            case 'notification':\n                this.handleNotification(data);\n                break;\n            case 'data_update':\n                this.emit('data_update', data);\n                break;\n            case 'user_activity':\n                this.emit('user_activity', data);\n                break;\n            case 'collaboration':\n                this.emit('collaboration', data);\n                break;\n            default:\n                this.emit(type, data);\n        }\n    }\n    handleNotification(data) {\n        const { title, message, type = 'info' } = data;\n        // Show toast notification\n        switch(type){\n            case 'success':\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_0__.toast.success(message || title);\n                break;\n            case 'error':\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_0__.toast.error(message || title);\n                break;\n            case 'warning':\n                (0,react_hot_toast__WEBPACK_IMPORTED_MODULE_0__.toast)(message || title, {\n                    icon: '⚠️'\n                });\n                break;\n            default:\n                (0,react_hot_toast__WEBPACK_IMPORTED_MODULE_0__.toast)(message || title);\n        }\n        // Emit to subscribers\n        this.emit('notification', data);\n    }\n    emit(eventType, data) {\n        const callbacks = this.listeners.get(eventType);\n        if (callbacks) {\n            callbacks.forEach((callback)=>{\n                try {\n                    callback(data);\n                } catch (error) {\n                    console.error(\"Error in WebSocket callback for \".concat(eventType, \":\"), error);\n                }\n            });\n        }\n    }\n    scheduleReconnect() {\n        this.clearReconnectTimer();\n        this.reconnectAttempts++;\n        const delay = Math.min(this.config.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1), 30000 // Max 30 seconds\n        );\n        console.log(\"\\uD83D\\uDD04 Reconnecting in \".concat(delay, \"ms (attempt \").concat(this.reconnectAttempts, \"/\").concat(this.config.maxReconnectAttempts, \")\"));\n        this.reconnectTimer = setTimeout(()=>{\n            this.connect().catch((error)=>{\n                console.error('Reconnection failed:', error);\n            });\n        }, delay);\n    }\n    clearReconnectTimer() {\n        if (this.reconnectTimer) {\n            clearTimeout(this.reconnectTimer);\n            this.reconnectTimer = null;\n        }\n    }\n    startHeartbeat() {\n        this.stopHeartbeat();\n        this.heartbeatTimer = setInterval(()=>{\n            this.send('heartbeat', {\n                timestamp: Date.now()\n            });\n        }, this.config.heartbeatInterval);\n    }\n    stopHeartbeat() {\n        if (this.heartbeatTimer) {\n            clearInterval(this.heartbeatTimer);\n            this.heartbeatTimer = null;\n        }\n    }\n    get isConnected() {\n        var _this_ws;\n        return ((_this_ws = this.ws) === null || _this_ws === void 0 ? void 0 : _this_ws.readyState) === WebSocket.OPEN;\n    }\n    get connectionState() {\n        if (!this.ws) return 'disconnected';\n        switch(this.ws.readyState){\n            case WebSocket.CONNECTING:\n                return 'connecting';\n            case WebSocket.OPEN:\n                return 'connected';\n            case WebSocket.CLOSING:\n                return 'closing';\n            case WebSocket.CLOSED:\n                return 'disconnected';\n            default:\n                return 'unknown';\n        }\n    }\n    constructor(config = {}){\n        this.ws = null;\n        this.reconnectAttempts = 0;\n        this.reconnectTimer = null;\n        this.heartbeatTimer = null;\n        this.listeners = new Map();\n        this.isConnecting = false;\n        this.isManualClose = false;\n        this.config = {\n            url: process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:5000',\n            reconnectInterval: 5000,\n            maxReconnectAttempts: 10,\n            heartbeatInterval: 30000,\n            ...config\n        };\n    }\n}\n// Singleton instance\nconst wsService = new WebSocketService();\n// React hook for WebSocket\nfunction useWebSocket() {\n    const [connectionState, setConnectionState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(wsService.connectionState);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(wsService.isConnected);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useWebSocket.useEffect\": ()=>{\n            const unsubscribeConnection = wsService.subscribe('connection', {\n                \"useWebSocket.useEffect.unsubscribeConnection\": (data)=>{\n                    setConnectionState(wsService.connectionState);\n                    setIsConnected(wsService.isConnected);\n                }\n            }[\"useWebSocket.useEffect.unsubscribeConnection\"]);\n            const unsubscribeError = wsService.subscribe('error', {\n                \"useWebSocket.useEffect.unsubscribeError\": (data)=>{\n                    console.error('WebSocket error:', data.error);\n                }\n            }[\"useWebSocket.useEffect.unsubscribeError\"]);\n            // Auto-connect\n            if (!wsService.isConnected) {\n                wsService.connect().catch({\n                    \"useWebSocket.useEffect\": (error)=>{\n                        console.error('Failed to connect WebSocket:', error);\n                    }\n                }[\"useWebSocket.useEffect\"]);\n            }\n            return ({\n                \"useWebSocket.useEffect\": ()=>{\n                    unsubscribeConnection();\n                    unsubscribeError();\n                }\n            })[\"useWebSocket.useEffect\"];\n        }\n    }[\"useWebSocket.useEffect\"], []);\n    return {\n        isConnected,\n        connectionState,\n        send: wsService.send.bind(wsService),\n        subscribe: wsService.subscribe.bind(wsService),\n        disconnect: wsService.disconnect.bind(wsService),\n        connect: wsService.connect.bind(wsService)\n    };\n}\n// Import useState for the hook\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/websocket-service.ts\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["framer-motion","lucide","vendors","default-_app-pages-browser_src_components_ui_button_tsx-_app-pages-browser_src_components_ui_-689fdc","main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cquang%5C%5CDesktop%5C%5Chackathon%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);