// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// User model with role-based access
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  avatar    String?
  password  String? // For credentials login
  role      Role     @default(STUDENT)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // NextAuth.js fields
  emailVerified DateTime?
  image         String?
  accounts      Account[]
  sessions      Session[]

  // Relations
  teachingClasses  Class[]          @relation("TeacherClasses")
  enrolledClasses  ClassMember[]
  assignments      Assignment[]
  todos            Todo[]
  aiLearningPaths  AiLearningPath[]
  notifications    Notification[]
  groupMemberships GroupMember[]
  chatMessages     ChatMessage[]

  @@map("users")
}

// NextAuth.js models
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verificationtokens")
}

// Core application models
enum Role {
  ADMIN
  TEACHER
  STUDENT
  GUEST
}

model Class {
  id          String   @id @default(cuid())
  name        String
  description String?
  subject     String
  inviteCode  String   @unique
  teacherId   String
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  teacher     User          @relation("TeacherClasses", fields: [teacherId], references: [id])
  members     ClassMember[]
  assignments Assignment[]
  groups      Group[]

  @@map("classes")
}

model ClassMember {
  id       String   @id @default(cuid())
  classId  String
  userId   String
  joinedAt DateTime @default(now())

  // Relations
  class Class @relation(fields: [classId], references: [id], onDelete: Cascade)
  user  User  @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([classId, userId])
  @@map("class_members")
}

model Assignment {
  id          String           @id @default(cuid())
  title       String
  description String
  deadline    DateTime
  attachments String? // JSON string of URLs
  classId     String
  teacherId   String
  status      AssignmentStatus @default(ACTIVE)
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt

  // Relations
  class   Class   @relation(fields: [classId], references: [id], onDelete: Cascade)
  teacher User    @relation(fields: [teacherId], references: [id])
  todos   Todo[]
  groups  Group[]

  @@map("assignments")
}

enum AssignmentStatus {
  DRAFT
  ACTIVE
  COMPLETED
  ARCHIVED
}

model Todo {
  id           String     @id @default(cuid())
  title        String
  description  String?
  deadline     DateTime?
  status       TodoStatus @default(PENDING)
  priority     Priority   @default(MEDIUM)
  source       TodoSource
  userId       String
  assignmentId String?
  aiPathId     String?
  groupId      String?
  order        Int        @default(0)
  completedAt  DateTime?
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt

  // Relations
  user       User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  assignment Assignment?     @relation(fields: [assignmentId], references: [id], onDelete: Cascade)
  aiPath     AiLearningPath? @relation(fields: [aiPathId], references: [id], onDelete: Cascade)
  group      Group?          @relation(fields: [groupId], references: [id], onDelete: Cascade)

  @@map("todos")
}

enum TodoStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  OVERDUE
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum TodoSource {
  TEACHER_ASSIGNMENT
  AI_LEARNING_PATH
  GROUP_TASK
  PERSONAL
}

model AiLearningPath {
  id          String   @id @default(cuid())
  title       String
  description String
  subject     String
  duration    Int // days
  difficulty  String
  userId      String
  isActive    Boolean  @default(true)
  progress    Float    @default(0) // 0-100
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user  User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  todos Todo[]
  steps AiLearningStep[]

  @@map("ai_learning_paths")
}

model AiLearningStep {
  id          String   @id @default(cuid())
  title       String
  description String
  day         Int
  resources   String? // JSON string of URLs, files
  pathId      String
  isCompleted Boolean  @default(false)
  createdAt   DateTime @default(now())

  // Relations
  path AiLearningPath @relation(fields: [pathId], references: [id], onDelete: Cascade)

  @@map("ai_learning_steps")
}

model Group {
  id           String   @id @default(cuid())
  name         String
  description  String?
  classId      String?
  assignmentId String?
  createdBy    String
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  class      Class?        @relation(fields: [classId], references: [id], onDelete: Cascade)
  assignment Assignment?   @relation(fields: [assignmentId], references: [id], onDelete: Cascade)
  members    GroupMember[]
  todos      Todo[]
  messages   ChatMessage[]

  @@map("groups")
}

model GroupMember {
  id       String    @id @default(cuid())
  groupId  String
  userId   String
  role     GroupRole @default(MEMBER)
  joinedAt DateTime  @default(now())

  // Relations
  group Group @relation(fields: [groupId], references: [id], onDelete: Cascade)
  user  User  @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([groupId, userId])
  @@map("group_members")
}

enum GroupRole {
  LEADER
  MEMBER
}

model ChatMessage {
  id        String   @id @default(cuid())
  content   String
  groupId   String
  userId    String
  createdAt DateTime @default(now())

  // Relations
  group Group @relation(fields: [groupId], references: [id], onDelete: Cascade)
  user  User  @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("chat_messages")
}

model Notification {
  id        String           @id @default(cuid())
  title     String
  message   String
  type      NotificationType
  userId    String
  isRead    Boolean          @default(false)
  data      Json? // Additional data
  createdAt DateTime         @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

enum NotificationType {
  ASSIGNMENT
  DEADLINE
  GROUP_INVITE
  AI_SUGGESTION
  SYSTEM
}
