/**
 * Models Index - Central Export for All Sequelize Models
 * Provides a single point of import for all database models
 */

const { sequelize } = require('../config/database');

// Import all models
const User = require('./User');
const Todo = require('./Todo');
const StudyGroup = require('./StudyGroup');
const Goal = require('./Goal');
const Event = require('./Event');
const Notification = require('./Notification');
const Exam = require('./Exam');
const Resource = require('./Resource');
const Assignment = require('./Assignment');
const Note = require('./Note');
const Progress = require('./Progress');
const StudyTimer = require('./StudyTimer');
const StudyStreak = require('./StudyStreak');
const StudyPlan = require('./StudyPlan');
const GPARecord = require('./GPARecord');
const CourseReview = require('./CourseReview');
const PeerTutoring = require('./PeerTutoring');
const Mentor = require('./Mentor');
const Feedback = require('./Feedback');
const AcademicEvent = require('./AcademicEvent');
const TrialCourse = require('./TrialCourse');
const ChatHistory = require('./ChatHistory');
const Message = require('./Message');
const Thread = require('./Thread');
const Post = require('./Post');
const Comment = require('./Comment');
const Group = require('./Group');
const Team = require('./Team');
const Workspace = require('./Workspace');
const Page = require('./Page');
const Block = require('./Block');

// Import associations
require('./associations');

// Export all models and sequelize instance
module.exports = {
  sequelize,
  User,
  Todo,
  StudyGroup,
  Goal,
  Event,
  Notification,
  Exam,
  Resource,
  Assignment,
  Note,
  Progress,
  StudyTimer,
  StudyStreak,
  StudyPlan,
  GPARecord,
  CourseReview,
  PeerTutoring,
  Mentor,
  Feedback,
  AcademicEvent,
  TrialCourse,
  ChatHistory,
  Message,
  Thread,
  Post,
  Comment,
  Group,
  Team,
  Workspace,
  Page,
  Block,
};
