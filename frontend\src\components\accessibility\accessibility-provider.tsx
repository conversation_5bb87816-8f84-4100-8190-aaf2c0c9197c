/**
 * Accessibility Provider
 * Provides comprehensive accessibility features and WCAG compliance
 */

'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Accessibility, 
  Eye, 
  EyeOff, 
  Type, 
  Contrast, 
  Volume2, 
  VolumeX,
  Settings,
  X,
  Minus,
  Plus
} from 'lucide-react';

interface AccessibilitySettings {
  highContrast: boolean;
  largeText: boolean;
  reducedMotion: boolean;
  screenReader: boolean;
  keyboardNavigation: boolean;
  fontSize: number;
  colorBlindMode: 'none' | 'protanopia' | 'deuteranopia' | 'tritanopia';
  soundEnabled: boolean;
  focusIndicator: boolean;
}

interface AccessibilityContextType {
  settings: AccessibilitySettings;
  updateSetting: <K extends keyof AccessibilitySettings>(
    key: K,
    value: AccessibilitySettings[K]
  ) => void;
  resetSettings: () => void;
  announceToScreenReader: (message: string) => void;
}

const defaultSettings: AccessibilitySettings = {
  highContrast: false,
  largeText: false,
  reducedMotion: false,
  screenReader: false,
  keyboardNavigation: true,
  fontSize: 16,
  colorBlindMode: 'none',
  soundEnabled: true,
  focusIndicator: true
};

const AccessibilityContext = createContext<AccessibilityContextType | undefined>(undefined);

export function useAccessibility() {
  const context = useContext(AccessibilityContext);
  if (!context) {
    throw new Error('useAccessibility must be used within AccessibilityProvider');
  }
  return context;
}

interface AccessibilityProviderProps {
  children: ReactNode;
}

export function AccessibilityProvider({ children }: AccessibilityProviderProps) {
  const [settings, setSettings] = useState<AccessibilitySettings>(defaultSettings);
  const [showPanel, setShowPanel] = useState(false);

  // Load settings from localStorage
  useEffect(() => {
    const savedSettings = localStorage.getItem('accessibility-settings');
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings);
        setSettings({ ...defaultSettings, ...parsed });
      } catch (error) {
        console.error('Failed to parse accessibility settings:', error);
      }
    }
  }, []);

  // Save settings to localStorage
  useEffect(() => {
    localStorage.setItem('accessibility-settings', JSON.stringify(settings));
    applyAccessibilitySettings(settings);
  }, [settings]);

  // Apply accessibility settings to DOM
  const applyAccessibilitySettings = (settings: AccessibilitySettings) => {
    const root = document.documentElement;
    
    // High contrast mode
    if (settings.highContrast) {
      root.classList.add('high-contrast');
    } else {
      root.classList.remove('high-contrast');
    }

    // Large text
    if (settings.largeText) {
      root.classList.add('large-text');
    } else {
      root.classList.remove('large-text');
    }

    // Reduced motion
    if (settings.reducedMotion) {
      root.classList.add('reduce-motion');
    } else {
      root.classList.remove('reduce-motion');
    }

    // Font size
    root.style.setProperty('--base-font-size', `${settings.fontSize}px`);

    // Color blind mode
    root.setAttribute('data-colorblind-mode', settings.colorBlindMode);

    // Focus indicator
    if (settings.focusIndicator) {
      root.classList.add('enhanced-focus');
    } else {
      root.classList.remove('enhanced-focus');
    }

    // Keyboard navigation
    if (settings.keyboardNavigation) {
      root.classList.add('keyboard-navigation');
    } else {
      root.classList.remove('keyboard-navigation');
    }
  };

  const updateSetting = <K extends keyof AccessibilitySettings>(
    key: K,
    value: AccessibilitySettings[K]
  ) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    
    // Announce changes to screen readers
    announceToScreenReader(`${key} ${value ? 'enabled' : 'disabled'}`);
  };

  const resetSettings = () => {
    setSettings(defaultSettings);
    announceToScreenReader('Accessibility settings reset to default');
  };

  const announceToScreenReader = (message: string) => {
    if (!settings.screenReader) return;

    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    
    document.body.appendChild(announcement);
    
    setTimeout(() => {
      document.body.removeChild(announcement);
    }, 1000);
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Alt + A: Toggle accessibility panel
      if (event.altKey && event.key === 'a') {
        event.preventDefault();
        setShowPanel(prev => !prev);
        announceToScreenReader('Accessibility panel toggled');
      }

      // Alt + H: Toggle high contrast
      if (event.altKey && event.key === 'h') {
        event.preventDefault();
        updateSetting('highContrast', !settings.highContrast);
      }

      // Alt + T: Toggle large text
      if (event.altKey && event.key === 't') {
        event.preventDefault();
        updateSetting('largeText', !settings.largeText);
      }

      // Alt + M: Toggle reduced motion
      if (event.altKey && event.key === 'm') {
        event.preventDefault();
        updateSetting('reducedMotion', !settings.reducedMotion);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [settings]);

  const contextValue: AccessibilityContextType = {
    settings,
    updateSetting,
    resetSettings,
    announceToScreenReader
  };

  return (
    <AccessibilityContext.Provider value={contextValue}>
      {children}
      
      {/* Accessibility Panel Toggle */}
      <Button
        className="fixed bottom-4 right-4 z-50 rounded-full w-12 h-12 p-0 shadow-lg"
        onClick={() => setShowPanel(!showPanel)}
        aria-label="Open accessibility settings"
        title="Accessibility Settings (Alt + A)"
      >
        <Accessibility className="h-5 w-5" />
      </Button>

      {/* Accessibility Panel */}
      <AnimatePresence>
        {showPanel && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            className="fixed bottom-20 right-4 z-50 w-80"
          >
            <Card className="shadow-xl">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg flex items-center">
                    <Accessibility className="h-5 w-5 mr-2" />
                    Accessibility
                  </CardTitle>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setShowPanel(false)}
                    aria-label="Close accessibility panel"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {/* High Contrast */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Contrast className="h-4 w-4" />
                    <span className="text-sm font-medium">High Contrast</span>
                  </div>
                  <Button
                    variant={settings.highContrast ? "default" : "outline"}
                    size="sm"
                    onClick={() => updateSetting('highContrast', !settings.highContrast)}
                    aria-pressed={settings.highContrast}
                  >
                    {settings.highContrast ? <Eye className="h-3 w-3" /> : <EyeOff className="h-3 w-3" />}
                  </Button>
                </div>

                {/* Large Text */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Type className="h-4 w-4" />
                    <span className="text-sm font-medium">Large Text</span>
                  </div>
                  <Button
                    variant={settings.largeText ? "default" : "outline"}
                    size="sm"
                    onClick={() => updateSetting('largeText', !settings.largeText)}
                    aria-pressed={settings.largeText}
                  >
                    {settings.largeText ? 'On' : 'Off'}
                  </Button>
                </div>

                {/* Font Size */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Font Size</span>
                    <span className="text-sm text-gray-500">{settings.fontSize}px</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => updateSetting('fontSize', Math.max(12, settings.fontSize - 2))}
                      disabled={settings.fontSize <= 12}
                      aria-label="Decrease font size"
                    >
                      <Minus className="h-3 w-3" />
                    </Button>
                    <div className="flex-1 bg-gray-200 h-2 rounded">
                      <div 
                        className="bg-blue-500 h-2 rounded transition-all"
                        style={{ width: `${((settings.fontSize - 12) / (24 - 12)) * 100}%` }}
                      />
                    </div>
                    <Button
                      variant="outline"
                      size="icon"
                      onClick={() => updateSetting('fontSize', Math.min(24, settings.fontSize + 2))}
                      disabled={settings.fontSize >= 24}
                      aria-label="Increase font size"
                    >
                      <Plus className="h-3 w-3" />
                    </Button>
                  </div>
                </div>

                {/* Reduced Motion */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Settings className="h-4 w-4" />
                    <span className="text-sm font-medium">Reduced Motion</span>
                  </div>
                  <Button
                    variant={settings.reducedMotion ? "default" : "outline"}
                    size="sm"
                    onClick={() => updateSetting('reducedMotion', !settings.reducedMotion)}
                    aria-pressed={settings.reducedMotion}
                  >
                    {settings.reducedMotion ? 'On' : 'Off'}
                  </Button>
                </div>

                {/* Sound */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {settings.soundEnabled ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
                    <span className="text-sm font-medium">Sound Effects</span>
                  </div>
                  <Button
                    variant={settings.soundEnabled ? "default" : "outline"}
                    size="sm"
                    onClick={() => updateSetting('soundEnabled', !settings.soundEnabled)}
                    aria-pressed={settings.soundEnabled}
                  >
                    {settings.soundEnabled ? 'On' : 'Off'}
                  </Button>
                </div>

                {/* Color Blind Mode */}
                <div className="space-y-2">
                  <span className="text-sm font-medium">Color Blind Support</span>
                  <select
                    value={settings.colorBlindMode}
                    onChange={(e) => updateSetting('colorBlindMode', e.target.value as any)}
                    className="w-full p-2 border rounded text-sm"
                    aria-label="Color blind mode"
                  >
                    <option value="none">None</option>
                    <option value="protanopia">Protanopia</option>
                    <option value="deuteranopia">Deuteranopia</option>
                    <option value="tritanopia">Tritanopia</option>
                  </select>
                </div>

                {/* Reset Button */}
                <Button
                  variant="outline"
                  onClick={resetSettings}
                  className="w-full"
                >
                  Reset to Default
                </Button>

                {/* Keyboard Shortcuts Info */}
                <div className="text-xs text-gray-500 space-y-1">
                  <p><strong>Keyboard Shortcuts:</strong></p>
                  <p>Alt + A: Toggle this panel</p>
                  <p>Alt + H: Toggle high contrast</p>
                  <p>Alt + T: Toggle large text</p>
                  <p>Alt + M: Toggle reduced motion</p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Screen Reader Announcements */}
      <div aria-live="polite" aria-atomic="true" className="sr-only" />
    </AccessibilityContext.Provider>
  );
}
