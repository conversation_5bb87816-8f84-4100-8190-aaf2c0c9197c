{"name": "fpt-unihub-nextjs", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint --fix", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@auth/prisma-adapter": "^2.7.4", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@headlessui/react": "^2.2.7", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.10.0", "@next/swc-win32-x64-msvc": "^15.4.5", "@prisma/client": "^6.1.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-form": "^0.1.7", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.3", "@tanstack/react-query": "^5.84.1", "autoprefixer": "^10.4.21", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "cloudinary": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^4.1.0", "framer-motion": "^12.23.12", "googleapis": "^144.0.0", "immer": "^10.1.1", "jsonwebtoken": "^9.0.2", "lottie-react": "^2.4.1", "lucide-react": "^0.468.0", "next": "15.4.5", "next-auth": "5.0.0-beta.25", "next-cloudinary": "^6.15.0", "next-themes": "^0.4.6", "openai": "^4.73.1", "pusher": "^5.2.0", "pusher-js": "^8.4.0-rc2", "react": "19.1.0", "react-day-picker": "^9.4.2", "react-dom": "19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.62.0", "react-hot-toast": "^2.4.1", "react-intersection-observer": "^9.16.0", "react-markdown": "^9.0.1", "recharts": "^2.15.4", "remark-gfm": "^4.0.0", "resend": "^4.0.1", "socket.io-client": "^4.8.1", "sonner": "^1.7.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.76", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9", "prisma": "^6.1.0", "tailwindcss": "^4", "tsx": "^4.19.2", "typescript": "^5"}, "engines": {"node": ">=18.0.0"}}