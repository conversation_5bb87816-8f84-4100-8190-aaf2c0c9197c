/**
 * Real-time data hook for FPT UniHub
 * Provides live data updates with WebSocket integration
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { toast } from 'react-hot-toast';

interface RealTimeDataState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

interface UseRealTimeDataOptions {
  refreshInterval?: number; // in milliseconds
  enableWebSocket?: boolean;
  retryAttempts?: number;
  retryDelay?: number;
}

export function useRealTimeData<T>(
  fetchFunction: () => Promise<T>,
  options: UseRealTimeDataOptions = {}
) {
  const {
    refreshInterval = 30000, // 30 seconds default
    enableWebSocket = false,
    retryAttempts = 3,
    retryDelay = 1000
  } = options;

  const [state, setState] = useState<RealTimeDataState<T>>({
    data: null,
    loading: true,
    error: null,
    lastUpdated: null
  });

  const retryCountRef = useRef(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const wsRef = useRef<WebSocket | null>(null);

  const fetchData = useCallback(async (showLoading = true) => {
    if (showLoading) {
      setState(prev => ({ ...prev, loading: true, error: null }));
    }

    try {
      const data = await fetchFunction();
      setState({
        data,
        loading: false,
        error: null,
        lastUpdated: new Date()
      });
      retryCountRef.current = 0;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error('Real-time data fetch error:', error);
      
      if (retryCountRef.current < retryAttempts) {
        retryCountRef.current++;
        setTimeout(() => fetchData(false), retryDelay * retryCountRef.current);
      } else {
        setState(prev => ({
          ...prev,
          loading: false,
          error: errorMessage
        }));
        toast.error(`Failed to fetch data: ${errorMessage}`);
      }
    }
  }, [fetchFunction, retryAttempts, retryDelay]);

  const setupWebSocket = useCallback(() => {
    if (!enableWebSocket) return;

    try {
      const wsUrl = process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:5000';
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('WebSocket connected for real-time updates');
      };

      wsRef.current.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          if (message.type === 'data_update') {
            fetchData(false); // Refresh data without showing loading
          }
        } catch (error) {
          console.error('WebSocket message parsing error:', error);
        }
      };

      wsRef.current.onclose = () => {
        console.log('WebSocket disconnected, attempting to reconnect...');
        setTimeout(setupWebSocket, 5000); // Reconnect after 5 seconds
      };

      wsRef.current.onerror = (error) => {
        console.error('WebSocket error:', error);
      };
    } catch (error) {
      console.error('WebSocket setup error:', error);
    }
  }, [enableWebSocket, fetchData]);

  const refresh = useCallback(() => {
    fetchData(true);
  }, [fetchData]);

  useEffect(() => {
    // Initial data fetch
    fetchData();

    // Setup polling interval
    if (refreshInterval > 0) {
      intervalRef.current = setInterval(() => {
        fetchData(false); // Don't show loading for background updates
      }, refreshInterval);
    }

    // Setup WebSocket if enabled
    setupWebSocket();

    // Cleanup
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, [fetchData, refreshInterval, setupWebSocket]);

  return {
    ...state,
    refresh,
    isStale: state.lastUpdated ? Date.now() - state.lastUpdated.getTime() > refreshInterval * 2 : false
  };
}

// Specialized hooks for common data types
export function useRealTimeStats() {
  return useRealTimeData(
    async () => {
      const response = await fetch('http://localhost:5000/api/dashboard/public-stats');
      const data = await response.json();
      if (!data.success) throw new Error(data.message || 'Failed to fetch stats');
      return data.data;
    },
    { refreshInterval: 60000, enableWebSocket: true } // Update every minute
  );
}

export function useRealTimeTestimonials() {
  return useRealTimeData(
    async () => {
      const response = await fetch('http://localhost:5000/api/dashboard/public-testimonials');
      const data = await response.json();
      if (!data.success) throw new Error(data.message || 'Failed to fetch testimonials');
      return data.data;
    },
    { refreshInterval: 300000 } // Update every 5 minutes
  );
}

export function useRealTimeUsers() {
  return useRealTimeData(
    async () => {
      const response = await fetch('http://localhost:5000/api/users');
      const data = await response.json();
      if (!data.success) throw new Error(data.message || 'Failed to fetch users');
      return data.data;
    },
    { refreshInterval: 30000, enableWebSocket: true }
  );
}

export function useRealTimeTodos(userId?: string) {
  return useRealTimeData(
    async () => {
      const url = userId 
        ? `http://localhost:5000/api/todo?userId=${userId}`
        : 'http://localhost:5000/api/todo';
      const response = await fetch(url);
      const data = await response.json();
      if (!data.success) throw new Error(data.message || 'Failed to fetch todos');
      return data.data;
    },
    { refreshInterval: 15000, enableWebSocket: true }
  );
}

export function useRealTimeNotifications() {
  return useRealTimeData(
    async () => {
      const response = await fetch('http://localhost:5000/api/notification');
      const data = await response.json();
      if (!data.success) throw new Error(data.message || 'Failed to fetch notifications');
      return data.data;
    },
    { refreshInterval: 10000, enableWebSocket: true }
  );
}

// Connection status hook
export function useConnectionStatus() {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [apiStatus, setApiStatus] = useState<'connected' | 'disconnected' | 'checking'>('checking');

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Check API status
    const checkApiStatus = async () => {
      try {
        const response = await fetch('http://localhost:5000/api/health', {
          method: 'GET',
          timeout: 5000
        } as any);
        setApiStatus(response.ok ? 'connected' : 'disconnected');
      } catch {
        setApiStatus('disconnected');
      }
    };

    checkApiStatus();
    const interval = setInterval(checkApiStatus, 30000);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      clearInterval(interval);
    };
  }, []);

  return { isOnline, apiStatus };
}
