const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const Notification = require('../models/Notification');
const User = require('../models/User');
const { Op } = require('sequelize');

function auth(req, res, next) {
  const token = req.headers.authorization?.split(' ')[1];
  if (!token) return res.status(401).json({ message: 'No token' });
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    req.userId = decoded.id;
    next();
  } catch {
    res.status(401).json({ message: 'Invalid token' });
  }
}

// Tạo notification mới
router.post('/', auth, async (req, res) => {
  try {
    const notification = await Notification.create({ ...req.body, user: req.userId });
    // Emit socket event
    const io = req.app.get('io');
    if (io) io.to(req.userId).emit('notification:created', notification);
    res.json({ success: true, data: notification, message: 'Tạo notification thành công' });
  } catch (error) {
    console.error('[NOTIFICATION API ERROR]', error);
    res.status(200).json({ success: false, message: error.message, data: null });
  }
});

// Lấy danh sách notification của user
router.get('/', auth, async (req, res) => {
  try {
    const notifications = await Notification.findAll({
      where: { userId: req.userId },
      order: [['createdAt', 'DESC']]
    });
    res.json({ success: true, data: notifications, message: 'Lấy danh sách notification thành công' });
  } catch (error) {
    console.error('[NOTIFICATION API ERROR]', error);
    res.status(200).json({ success: false, message: error.message, data: null });
  }
});

// Lấy chi tiết notification
router.get('/:id', auth, async (req, res) => {
  try {
    const notification = await Notification.findByPk(req.params.id);
    if (!notification) {
      res.status(404).json({ success: false, message: 'Notification not found', data: null });
      return;
    }
    res.json({ success: true, data: notification, message: 'Lấy chi tiết notification thành công' });
  } catch (error) {
    console.error('[NOTIFICATION API ERROR]', error);
    res.status(200).json({ success: false, message: error.message, data: null });
  }
});

// Đánh dấu notification đã đọc
router.put('/:id/read', auth, async (req, res) => {
  try {
    const [updatedRowsCount] = await Notification.update(
      { isRead: true },
      {
        where: { id: req.params.id, userId: req.userId },
        returning: true
      }
    );
    const notification = updatedRowsCount > 0 ? await Notification.findByPk(req.params.id) : null;
    if (!notification) {
      res.status(404).json({ success: false, message: 'Notification not found', data: null });
      return;
    }
    // Emit socket event
    const io = req.app.get('io');
    if (io) io.to(req.userId).emit('notification:updated', notification);
    res.json({ success: true, data: notification, message: 'Đánh dấu notification đã đọc thành công' });
  } catch (error) {
    console.error('[NOTIFICATION API ERROR]', error);
    res.status(200).json({ success: false, message: error.message, data: null });
  }
});

// Đánh dấu tất cả notifications đã đọc
router.put('/read-all', auth, async (req, res) => {
  try {
    await Notification.update(
      { isRead: true },
      { where: { userId: req.userId, isRead: false } }
    );
    // Emit socket event (cập nhật tất cả)
    const io = req.app.get('io');
    if (io) io.to(req.userId).emit('notification:updated', { allRead: true });
    res.json({ success: true, data: null, message: 'Đánh dấu tất cả notifications đã đọc thành công' });
  } catch (error) {
    console.error('[NOTIFICATION API ERROR]', error);
    res.status(200).json({ success: false, message: error.message, data: null });
  }
});

// Lấy số lượng notifications chưa đọc
router.get('/unread/count', auth, async (req, res) => {
  try {
    const count = await Notification.count({ where: { userId: req.userId, isRead: false } });
    res.json({ success: true, data: { count }, message: 'Lấy số lượng notifications chưa đọc thành công' });
  } catch (error) {
    console.error('[NOTIFICATION API ERROR]', error);
    res.status(200).json({ success: false, message: error.message, data: null });
  }
});

// Cập nhật notification (ví dụ: đánh dấu đã đọc)
router.put('/:id', auth, async (req, res) => {
  try {
    const [updatedRowsCount] = await Notification.update(
      req.body,
      {
        where: { id: req.params.id, userId: req.userId },
        returning: true
      }
    );
    const notification = updatedRowsCount > 0 ? await Notification.findByPk(req.params.id) : null;
    if (!notification) {
      res.status(404).json({ success: false, message: 'Notification not found', data: null });
      return;
    }
    // Emit socket event
    const io = req.app.get('io');
    if (io) io.to(req.userId).emit('notification:updated', notification);
    res.json({ success: true, data: notification, message: 'Cập nhật notification thành công' });
  } catch (error) {
    console.error('[NOTIFICATION API ERROR]', error);
    res.status(200).json({ success: false, message: error.message, data: null });
  }
});

// Xóa notification
router.delete('/:id', auth, async (req, res) => {
  try {
    const notification = await Notification.findOne({ where: { id: req.params.id, userId: req.userId } });
    if (notification) {
      await notification.destroy();
    }
    if (!notification) {
      res.status(404).json({ success: false, message: 'Notification not found', data: null });
      return;
    }
    // Emit socket event
    const io = req.app.get('io');
    if (io) io.to(req.userId).emit('notification:deleted', notification.id);
    res.json({ success: true, data: null, message: 'Xóa notification thành công' });
  } catch (error) {
    console.error('[NOTIFICATION API ERROR]', error);
    res.status(200).json({ success: false, message: error.message, data: null });
  }
});

module.exports = router; 