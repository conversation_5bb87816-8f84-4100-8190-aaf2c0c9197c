{"duration":"445ms","ip":"::1","level":"error","message":"HTTP Request Error","method":"GET","service":"fpt-unihub-backend","status":500,"timestamp":"2025-08-05T18:48:49.384Z","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"202ms","ip":"::1","level":"error","message":"HTTP Request Error","method":"GET","service":"fpt-unihub-backend","status":500,"timestamp":"2025-08-05T21:51:14.288Z","url":"/public-stats","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"365ms","ip":"::1","level":"error","message":"HTTP Request Error","method":"GET","service":"fpt-unihub-backend","status":500,"timestamp":"2025-08-05T21:51:16.025Z","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"59ms","ip":"::1","level":"error","message":"HTTP Request Error","method":"GET","service":"fpt-unihub-backend","status":500,"timestamp":"2025-08-05T22:06:30.064Z","url":"/public-stats","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
{"duration":"111ms","ip":"::1","level":"error","message":"HTTP Request Error","method":"GET","service":"fpt-unihub-backend","status":500,"timestamp":"2025-08-05T22:06:31.493Z","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","userId":"anonymous"}
