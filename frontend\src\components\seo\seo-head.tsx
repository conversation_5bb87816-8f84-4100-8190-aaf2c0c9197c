/**
 * SEO Head Component
 * Provides comprehensive SEO optimization for all pages
 */

import Head from 'next/head';
import { useRouter } from 'next/router';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'profile';
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  section?: string;
  tags?: string[];
  noindex?: boolean;
  nofollow?: boolean;
  canonical?: string;
}

const defaultSEO = {
  title: 'FPT UniHub - AI-Powered Learning Platform for FPT University',
  description: 'Transform your learning experience with FPT UniHub. AI-powered tutoring, smart todo management, collaborative study groups, and real-time progress tracking for FPT University students.',
  keywords: [
    'FPT University',
    'AI learning',
    'study platform',
    'education technology',
    'student portal',
    'collaborative learning',
    'AI tutor',
    'academic management',
    'study groups',
    'learning analytics'
  ],
  image: '/images/og-image.jpg',
  type: 'website' as const,
  author: 'FPT UniHub Team'
};

export function SEOHead({
  title,
  description,
  keywords = [],
  image,
  url,
  type = 'website',
  publishedTime,
  modifiedTime,
  author,
  section,
  tags = [],
  noindex = false,
  nofollow = false,
  canonical
}: SEOProps) {
  const router = useRouter();
  
  const seoTitle = title 
    ? `${title} | FPT UniHub`
    : defaultSEO.title;
  
  const seoDescription = description || defaultSEO.description;
  const seoKeywords = [...defaultSEO.keywords, ...keywords].join(', ');
  const seoImage = image || defaultSEO.image;
  const seoUrl = url || `${process.env.NEXT_PUBLIC_SITE_URL}${router.asPath}`;
  const seoAuthor = author || defaultSEO.author;

  const robotsContent = [
    noindex ? 'noindex' : 'index',
    nofollow ? 'nofollow' : 'follow'
  ].join(', ');

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{seoTitle}</title>
      <meta name="description" content={seoDescription} />
      <meta name="keywords" content={seoKeywords} />
      <meta name="author" content={seoAuthor} />
      <meta name="robots" content={robotsContent} />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />
      <meta name="language" content="English" />
      <meta name="theme-color" content="#3B82F6" />
      
      {/* Canonical URL */}
      <link rel="canonical" href={canonical || seoUrl} />
      
      {/* Open Graph Meta Tags */}
      <meta property="og:type" content={type} />
      <meta property="og:title" content={seoTitle} />
      <meta property="og:description" content={seoDescription} />
      <meta property="og:image" content={seoImage} />
      <meta property="og:url" content={seoUrl} />
      <meta property="og:site_name" content="FPT UniHub" />
      <meta property="og:locale" content="en_US" />
      
      {/* Article specific Open Graph tags */}
      {type === 'article' && (
        <>
          {publishedTime && (
            <meta property="article:published_time" content={publishedTime} />
          )}
          {modifiedTime && (
            <meta property="article:modified_time" content={modifiedTime} />
          )}
          {author && (
            <meta property="article:author" content={author} />
          )}
          {section && (
            <meta property="article:section" content={section} />
          )}
          {tags.map((tag, index) => (
            <meta key={index} property="article:tag" content={tag} />
          ))}
        </>
      )}
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:site" content="@FPTUniHub" />
      <meta name="twitter:creator" content="@FPTUniHub" />
      <meta name="twitter:title" content={seoTitle} />
      <meta name="twitter:description" content={seoDescription} />
      <meta name="twitter:image" content={seoImage} />
      
      {/* Additional Meta Tags for Education */}
      <meta name="category" content="Education" />
      <meta name="coverage" content="Worldwide" />
      <meta name="distribution" content="Global" />
      <meta name="rating" content="General" />
      <meta name="revisit-after" content="1 days" />
      
      {/* Structured Data for Educational Organization */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "EducationalOrganization",
            "name": "FPT UniHub",
            "description": seoDescription,
            "url": seoUrl,
            "logo": {
              "@type": "ImageObject",
              "url": `${process.env.NEXT_PUBLIC_SITE_URL}/images/logo.png`
            },
            "sameAs": [
              "https://www.facebook.com/FPTUniHub",
              "https://www.twitter.com/FPTUniHub",
              "https://www.linkedin.com/company/fptunihub"
            ],
            "contactPoint": {
              "@type": "ContactPoint",
              "telephone": "+84-***********",
              "contactType": "Customer Service",
              "availableLanguage": ["English", "Vietnamese"]
            },
            "address": {
              "@type": "PostalAddress",
              "streetAddress": "FPT University Campus",
              "addressLocality": "Ho Chi Minh City",
              "addressCountry": "Vietnam"
            }
          })
        }}
      />
      
      {/* Structured Data for Software Application */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "SoftwareApplication",
            "name": "FPT UniHub",
            "description": seoDescription,
            "url": seoUrl,
            "applicationCategory": "EducationalApplication",
            "operatingSystem": "Web Browser",
            "offers": {
              "@type": "Offer",
              "price": "0",
              "priceCurrency": "USD"
            },
            "aggregateRating": {
              "@type": "AggregateRating",
              "ratingValue": "4.8",
              "ratingCount": "1250"
            },
            "author": {
              "@type": "Organization",
              "name": "FPT UniHub Team"
            }
          })
        }}
      />
      
      {/* Favicons */}
      <link rel="icon" type="image/x-icon" href="/favicon.ico" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="manifest" href="/site.webmanifest" />
      
      {/* Preconnect to external domains */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="preconnect" href="https://api.openai.com" />
      
      {/* DNS Prefetch */}
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//fonts.gstatic.com" />
      <link rel="dns-prefetch" href="//api.openai.com" />
      
      {/* Preload critical resources */}
      <link
        rel="preload"
        href="/fonts/inter-var.woff2"
        as="font"
        type="font/woff2"
        crossOrigin="anonymous"
      />
    </Head>
  );
}

// Page-specific SEO configurations
export const pageSEO = {
  home: {
    title: 'AI-Powered Learning Platform for FPT University Students',
    description: 'Join thousands of FPT University students using AI-powered learning tools. Smart tutoring, collaborative study groups, and personalized learning paths.',
    keywords: ['FPT University', 'AI learning', 'student platform', 'education technology']
  },
  
  dashboard: {
    title: 'Student Dashboard',
    description: 'Your personalized learning dashboard with AI insights, progress tracking, and smart recommendations.',
    keywords: ['student dashboard', 'learning analytics', 'progress tracking']
  },
  
  aiTutor: {
    title: 'AI Tutor - Personalized Learning Assistant',
    description: 'Get instant help with your studies using our advanced AI tutor. Available 24/7 for all subjects.',
    keywords: ['AI tutor', 'learning assistant', 'study help', 'artificial intelligence']
  },
  
  classes: {
    title: 'Classes & Courses',
    description: 'Manage your classes, view assignments, and collaborate with classmates in one place.',
    keywords: ['class management', 'course materials', 'assignments', 'collaboration']
  },
  
  studyGroups: {
    title: 'Study Groups - Collaborative Learning',
    description: 'Join or create study groups with your peers. Share knowledge and learn together.',
    keywords: ['study groups', 'collaborative learning', 'peer learning', 'group study']
  },
  
  forum: {
    title: 'Student Forum - Academic Discussions',
    description: 'Connect with fellow students, ask questions, and share knowledge in our academic forum.',
    keywords: ['student forum', 'academic discussions', 'Q&A', 'knowledge sharing']
  },
  
  resources: {
    title: 'Learning Resources & Materials',
    description: 'Access a comprehensive library of learning resources, study materials, and academic tools.',
    keywords: ['learning resources', 'study materials', 'academic resources', 'educational content']
  },
  
  profile: {
    title: 'Student Profile',
    description: 'Manage your profile, track your achievements, and customize your learning experience.',
    keywords: ['student profile', 'achievements', 'learning progress', 'account settings']
  }
};

// SEO utility functions
export const generatePageSEO = (page: keyof typeof pageSEO, customData?: Partial<SEOProps>) => {
  const pageData = pageSEO[page];
  return {
    ...pageData,
    ...customData
  };
};
