/**
 * Real-time Notification System
 * Provides live notifications, alerts, and updates
 */

'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useWebSocket } from '@/lib/websocket-service';
import { useAuth } from '@/lib/auth-context';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Bell, 
  X, 
  Check, 
  AlertCircle, 
  Info, 
  CheckCircle, 
  XCircle,
  Clock,
  User,
  BookOpen,
  Calendar,
  MessageSquare,
  Trophy,
  Zap
} from 'lucide-react';
import { toast } from 'react-hot-toast';

export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error' | 'assignment' | 'message' | 'achievement';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  actionUrl?: string;
  actionLabel?: string;
  userId?: string;
  userName?: string;
  userAvatar?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: 'system' | 'social' | 'academic' | 'reminder';
}

interface NotificationSystemProps {
  maxNotifications?: number;
  showToasts?: boolean;
  autoMarkAsRead?: boolean;
}

export function RealTimeNotifications({ 
  maxNotifications = 50,
  showToasts = true,
  autoMarkAsRead = false
}: NotificationSystemProps) {
  // const { user } = useAuth();
  const user = null; // Temporary fix
  const { isConnected, send, subscribe } = useWebSocket();
  
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [showPanel, setShowPanel] = useState(false);
  const [filter, setFilter] = useState<'all' | 'unread' | 'academic' | 'social'>('all');

  useEffect(() => {
    if (!isConnected || !user) return;

    // Subscribe to notifications
    const unsubscribeNotifications = subscribe('notification', (data) => {
      const notification: Notification = {
        id: data.id || `notif_${Date.now()}`,
        type: data.type || 'info',
        title: data.title,
        message: data.message,
        timestamp: new Date(data.timestamp || Date.now()),
        read: false,
        actionUrl: data.actionUrl,
        actionLabel: data.actionLabel,
        userId: data.userId,
        userName: data.userName,
        userAvatar: data.userAvatar,
        priority: data.priority || 'medium',
        category: data.category || 'system'
      };

      setNotifications(prev => {
        const updated = [notification, ...prev].slice(0, maxNotifications);
        return updated;
      });

      // Show toast if enabled
      if (showToasts) {
        showNotificationToast(notification);
      }

      // Auto mark as read if enabled
      if (autoMarkAsRead) {
        setTimeout(() => {
          markAsRead(notification.id);
        }, 3000);
      }
    });

    // Request initial notifications
    send('get_notifications', { userId: user.id });

    return () => {
      unsubscribeNotifications();
    };
  }, [isConnected, user, maxNotifications, showToasts, autoMarkAsRead, send, subscribe]);

  const showNotificationToast = (notification: Notification) => {
    const toastOptions = {
      duration: notification.priority === 'urgent' ? 8000 : 4000,
      position: 'top-right' as const,
    };

    switch (notification.type) {
      case 'success':
      case 'achievement':
        toast.success(notification.message, toastOptions);
        break;
      case 'error':
        toast.error(notification.message, toastOptions);
        break;
      case 'warning':
        toast(notification.message, { ...toastOptions, icon: '⚠️' });
        break;
      default:
        toast(notification.message, toastOptions);
    }
  };

  const markAsRead = (notificationId: string) => {
    setNotifications(prev =>
      prev.map(notif =>
        notif.id === notificationId ? { ...notif, read: true } : notif
      )
    );

    send('mark_notification_read', { notificationId });
  };

  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notif => ({ ...notif, read: true }))
    );

    send('mark_all_notifications_read', { userId: user?.id });
  };

  const deleteNotification = (notificationId: string) => {
    setNotifications(prev =>
      prev.filter(notif => notif.id !== notificationId)
    );

    send('delete_notification', { notificationId });
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success': return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error': return <XCircle className="h-5 w-5 text-red-500" />;
      case 'warning': return <AlertCircle className="h-5 w-5 text-yellow-500" />;
      case 'assignment': return <BookOpen className="h-5 w-5 text-blue-500" />;
      case 'message': return <MessageSquare className="h-5 w-5 text-purple-500" />;
      case 'achievement': return <Trophy className="h-5 w-5 text-yellow-500" />;
      default: return <Info className="h-5 w-5 text-blue-500" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'border-l-red-500 bg-red-50';
      case 'high': return 'border-l-orange-500 bg-orange-50';
      case 'medium': return 'border-l-blue-500 bg-blue-50';
      default: return 'border-l-gray-500 bg-gray-50';
    }
  };

  const filteredNotifications = notifications.filter(notif => {
    switch (filter) {
      case 'unread': return !notif.read;
      case 'academic': return notif.category === 'academic';
      case 'social': return notif.category === 'social';
      default: return true;
    }
  });

  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <div className="relative">
      {/* Notification Bell */}
      <Button
        variant="ghost"
        size="icon"
        onClick={() => setShowPanel(!showPanel)}
        className="relative"
      >
        <Bell className="h-5 w-5" />
        {unreadCount > 0 && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            className="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center"
          >
            {unreadCount > 99 ? '99+' : unreadCount}
          </motion.div>
        )}
      </Button>

      {/* Notification Panel */}
      <AnimatePresence>
        {showPanel && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -10 }}
            transition={{ duration: 0.2 }}
            className="absolute right-0 top-full mt-2 w-96 z-50"
          >
            <Card className="shadow-xl border-0">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">Notifications</CardTitle>
                  <div className="flex items-center space-x-2">
                    {unreadCount > 0 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={markAllAsRead}
                        className="text-xs"
                      >
                        Mark all read
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => setShowPanel(false)}
                      className="h-6 w-6"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* Filter Tabs */}
                <div className="flex space-x-1 mt-3">
                  {['all', 'unread', 'academic', 'social'].map((filterType) => (
                    <Button
                      key={filterType}
                      variant={filter === filterType ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setFilter(filterType as any)}
                      className="text-xs capitalize"
                    >
                      {filterType}
                      {filterType === 'unread' && unreadCount > 0 && (
                        <Badge className="ml-1 h-4 w-4 p-0 text-xs">
                          {unreadCount}
                        </Badge>
                      )}
                    </Button>
                  ))}
                </div>
              </CardHeader>

              <CardContent className="p-0">
                <div className="max-h-96 overflow-y-auto">
                  {filteredNotifications.length === 0 ? (
                    <div className="p-6 text-center text-gray-500">
                      <Bell className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p>No notifications</p>
                    </div>
                  ) : (
                    <div className="space-y-1">
                      {filteredNotifications.map((notification) => (
                        <motion.div
                          key={notification.id}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: 20 }}
                          className={`p-4 border-l-4 hover:bg-gray-50 cursor-pointer transition-colors ${
                            getPriorityColor(notification.priority)
                          } ${!notification.read ? 'bg-blue-50' : 'bg-white'}`}
                          onClick={() => !notification.read && markAsRead(notification.id)}
                        >
                          <div className="flex items-start space-x-3">
                            <div className="flex-shrink-0 mt-1">
                              {getNotificationIcon(notification.type)}
                            </div>
                            
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center justify-between">
                                <h4 className={`text-sm font-medium ${
                                  !notification.read ? 'text-gray-900' : 'text-gray-700'
                                }`}>
                                  {notification.title}
                                </h4>
                                <div className="flex items-center space-x-1">
                                  {!notification.read && (
                                    <div className="w-2 h-2 bg-blue-500 rounded-full" />
                                  )}
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      deleteNotification(notification.id);
                                    }}
                                    className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                                  >
                                    <X className="h-3 w-3" />
                                  </Button>
                                </div>
                              </div>
                              
                              <p className="text-sm text-gray-600 mt-1">
                                {notification.message}
                              </p>
                              
                              <div className="flex items-center justify-between mt-2">
                                <div className="flex items-center space-x-2 text-xs text-gray-500">
                                  <Clock className="h-3 w-3" />
                                  <span>
                                    {notification.timestamp.toLocaleTimeString()}
                                  </span>
                                </div>
                                
                                {notification.actionUrl && (
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="text-xs"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      window.location.href = notification.actionUrl!;
                                    }}
                                  >
                                    {notification.actionLabel || 'View'}
                                  </Button>
                                )}
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
